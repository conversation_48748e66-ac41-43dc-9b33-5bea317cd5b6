const axios = require('axios');
require('dotenv').config();

// Import all providers
const HetznerProvider = require('./providers/hetzner');
const VultrProvider = require('./providers/vultr');
const LinodeProvider = require('./providers/linode');
const DigitalOceanProvider = require('./providers/digitalocean');
const AWSLightsailProvider = require('./providers/aws-lightsail');
const AzureProvider = require('./providers/azure');

async function realTimeMonitoring() {
    console.log('🔄 Real-Time Bandwidth Monitoring');
    console.log('==================================');
    console.log('');
    console.log('This script will check bandwidth usage every 2 minutes');
    console.log('to help you see when providers start detecting your traffic.');
    console.log('');
    console.log('Press Ctrl+C to stop monitoring');
    console.log('');

    const providers = [
        {
            name: 'Hetz<PERSON>',
            provider: new HetznerProvider(process.env.HCLOUD_TOKEN),
            configured: !!process.env.HCLOUD_TOKEN,
            lastUsage: 0
        },
        {
            name: 'Vultr',
            provider: new VultrProvider(process.env.VULTR_API_KEY),
            configured: !!process.env.VULTR_API_KEY,
            lastUsage: 0
        },
        {
            name: 'Linode',
            provider: new LinodeProvider(process.env.LINODE_TOKEN),
            configured: !!process.env.LINODE_TOKEN,
            lastUsage: 0
        },
        {
            name: 'DigitalOcean',
            provider: new DigitalOceanProvider(process.env.DIGITALOCEAN_TOKEN),
            configured: !!process.env.DIGITALOCEAN_TOKEN,
            lastUsage: 0
        },
        {
            name: 'AWS Lightsail',
            provider: new AWSLightsailProvider(
                process.env.AWS_ACCESS_KEY_ID,
                process.env.AWS_SECRET_ACCESS_KEY,
                process.env.AWS_REGION || 'us-east-1'
            ),
            configured: !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY),
            lastUsage: 0
        },
        {
            name: 'Azure',
            provider: new AzureProvider(
                process.env.AZURE_CLIENT_ID,
                process.env.AZURE_CLIENT_SECRET,
                process.env.AZURE_TENANT_ID,
                process.env.AZURE_SUBSCRIPTION_ID
            ),
            configured: !!(process.env.AZURE_CLIENT_ID && process.env.AZURE_CLIENT_SECRET),
            lastUsage: 0
        }
    ];

    let checkCount = 0;

    async function checkAllProviders() {
        checkCount++;
        const currentTime = new Date();
        
        console.log(`\n${'='.repeat(80)}`);
        console.log(`🕐 Check #${checkCount} - ${currentTime.toLocaleTimeString()}`);
        console.log(`${'='.repeat(80)}`);

        for (const providerInfo of providers) {
            if (!providerInfo.configured) continue;

            try {
                const servers = await providerInfo.provider.listServers();
                
                if (servers.length === 0) continue;

                for (const server of servers) {
                    try {
                        let usage;
                        
                        // Handle special cases
                        if (providerInfo.name === 'AWS Lightsail' && server.region) {
                            usage = await providerInfo.provider.getBandwidthUsage(server.id, server.region);
                        } else {
                            usage = await providerInfo.provider.getBandwidthUsage(server.id);
                        }
                        
                        if (usage) {
                            const usageGB = usage.outgoingBytes / (1024 ** 3);
                            const usageMB = usageGB * 1024;
                            
                            // Check if usage changed
                            const changed = Math.abs(usageGB - providerInfo.lastUsage) > 0.000001;
                            const changeIndicator = changed ? '📈 CHANGED!' : '';
                            
                            if (usageGB > 0) {
                                console.log(`✅ ${providerInfo.name.padEnd(15)} ${usageMB.toFixed(2).padStart(8)} MB ${changeIndicator}`);
                            } else {
                                console.log(`⏳ ${providerInfo.name.padEnd(15)} ${usageMB.toFixed(2).padStart(8)} MB (waiting...)`);
                            }
                            
                            providerInfo.lastUsage = usageGB;
                        } else {
                            console.log(`❌ ${providerInfo.name.padEnd(15)} Error getting data`);
                        }
                    } catch (error) {
                        console.log(`❌ ${providerInfo.name.padEnd(15)} ${error.message}`);
                    }
                }
            } catch (error) {
                console.log(`❌ ${providerInfo.name.padEnd(15)} Provider error: ${error.message}`);
            }
        }

        console.log(`\n💡 Next check in 2 minutes...`);
    }

    // Initial check
    await checkAllProviders();

    // Set up interval for every 2 minutes
    const interval = setInterval(async () => {
        try {
            await checkAllProviders();
        } catch (error) {
            console.error('Error during monitoring:', error);
        }
    }, 2 * 60 * 1000); // 2 minutes

    // Handle Ctrl+C gracefully
    process.on('SIGINT', () => {
        console.log('\n\n🛑 Monitoring stopped by user');
        clearInterval(interval);
        process.exit(0);
    });
}

// Show traffic generation commands
console.log('🚀 Traffic Generation Commands');
console.log('==============================');
console.log('');
console.log('If you want to generate more traffic while monitoring:');
console.log('');
console.log('SSH into each VPS and run:');
console.log('  curl -o /dev/null http://speedtest.tele2.net/1GB.zip');
console.log('');
console.log('Or for a smaller test:');
console.log('  curl -o /dev/null http://speedtest.tele2.net/100MB.zip');
console.log('');
console.log('Starting monitoring in 3 seconds...');
console.log('');

setTimeout(() => {
    realTimeMonitoring().catch(console.error);
}, 3000);
