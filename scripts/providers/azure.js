const { DefaultAzureCredential } = require("@azure/identity");
const { NetworkManagementClient } = require("@azure/arm-network");
const { ResourceManagementClient } = require("@azure/arm-resources");
const { ComputeManagementClient } = require("@azure/arm-compute");
const { MetricsQueryClient } = require("@azure/monitor-query");

class AzureProvider {
    constructor(clientId, clientSecret, tenantId, subscriptionId) {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.tenantId = tenantId;
        this.subscriptionId = subscriptionId;
        this.name = "Azure";
        
        this.credential = this.clientId ? new DefaultAzureCredential({
            tenantId: this.tenantId,
            clientId: this.clientId,
            clientSecret: this.clientSecret,
        }) : null;
        
        this.networkClient = null;
        this.resourceClient = null;
        this.computeClient = null;
        this.metricsClient = null;
    }

    async initClients() {
        if (!this.credential) {
            return false;
        }
        if (!this.networkClient) {
            this.networkClient = new NetworkManagementClient(this.credential, this.subscriptionId);
        }
        if (!this.resourceClient) {
            this.resourceClient = new ResourceManagementClient(this.credential, this.subscriptionId);
        }
        if (!this.computeClient) {
            this.computeClient = new ComputeManagementClient(this.credential, this.subscriptionId);
        }
        if (!this.metricsClient) {
            this.metricsClient = new MetricsQueryClient(this.credential);
        }
        return true;
    }

    async getBandwidthUsage(vmId) {
        const initialized = await this.initClients();
        if (!initialized) {
            console.log("Azure: No credentials configured, skipping");
            return null;
        }

        try {
            // Parse VM ID to get resource group and VM name
            const parts = vmId.split("/");
            const resourceGroupName = parts[4];
            const vmName = parts[8];

            // Get VM details to determine size and potential transfer limits
            const vm = await this.computeClient.virtualMachines.get(resourceGroupName, vmName);

            // Azure doesn't have explicit transfer limits like other providers
            // Most VMs have unlimited outbound transfer within Azure regions
            // We'll use a high default limit (100TB) since Azure charges per GB after free tier
            const includedBytes = 100 * (1024 ** 4); // 100TB default

            // Get network metrics for the last 24 hours
            const now = new Date();
            const oneDayAgo = new Date(now.getTime() - (24 * 60 * 60 * 1000));

            let totalOutgoingBytes = 0;

            try {
                // Query Azure Monitor for both Network In and Network Out metrics
                // Downloads (like curl) show up as Network In Total
                const metricsResponse = await this.metricsClient.queryResource(
                    vmId,
                    ["Network In Total", "Network Out Total"],
                    {
                        granularity: "PT1H" // 1 hour granularity
                        // Let Azure use default timespan (last hour)
                    }
                );

                if (metricsResponse.metrics && metricsResponse.metrics.length > 0) {
                    let networkInBytes = 0;
                    let networkOutBytes = 0;

                    // Process all returned metrics
                    for (const metric of metricsResponse.metrics) {
                        if (metric.timeseries && metric.timeseries.length > 0) {
                            const timeseries = metric.timeseries[0];

                            if (timeseries.data && timeseries.data.length > 0) {
                                // Sum all the data points (each point is total bytes for that hour)
                                const totalBytes = timeseries.data.reduce((sum, dataPoint) => {
                                    return sum + (dataPoint.total || 0);
                                }, 0);

                                if (metric.name === 'Network In Total') {
                                    networkInBytes = totalBytes;
                                } else if (metric.name === 'Network Out Total') {
                                    networkOutBytes = totalBytes;
                                }
                            }
                        }
                    }

                    // For bandwidth monitoring, we typically track outgoing traffic
                    // But downloads (curl) show up as incoming traffic
                    // Use outgoing as primary, but include incoming for reference
                    totalOutgoingBytes = networkOutBytes;

                    console.log(`Azure: Got bandwidth data for VM ${vmName}:`);
                    console.log(`Azure: Network Out: ${(networkOutBytes / (1024**3)).toFixed(3)} GB`);
                    console.log(`Azure: Network In: ${(networkInBytes / (1024**3)).toFixed(3)} GB (downloads)`);
                    console.log(`Azure: Using Network Out for monitoring: ${(totalOutgoingBytes / (1024**3)).toFixed(3)} GB`);

                    if (networkInBytes > 100 * 1024 * 1024) { // More than 100MB incoming
                        console.log(`Azure: Note: Large incoming traffic detected (${(networkInBytes / (1024**3)).toFixed(3)} GB) - likely downloads`);
                    }
                } else {
                    console.log(`Azure: No metrics found for VM ${vmName}`);
                }

            } catch (metricsError) {
                if (metricsError.message.includes('not authorized') || metricsError.message.includes('Forbidden')) {
                    console.log(`Azure: Monitor permissions not available for ${vmName}.`);
                    console.log(`💡 Add Azure Monitor Reader role to your service principal for bandwidth monitoring.`);
                } else {
                    console.log(`Azure: Monitor metrics error for ${vmName}:`, metricsError.message);
                }
                // For new VMs or when metrics are unavailable, return 0 usage
                totalOutgoingBytes = 0;
            }

            return {
                outgoingBytes: totalOutgoingBytes,
                includedBytes: includedBytes
            };

        } catch (error) {
            console.error(`Azure: Failed to get bandwidth for VM ${vmId}:`, error.message);
            return null;
        }
    }

    async stopInstance(vmId) {
        const initialized = await this.initClients();
        if (!initialized) {
            console.log("Azure: No credentials configured, skipping");
            return false;
        }
        try {
            // Assuming vmId is in the format /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Compute/virtualMachines/{vmName}
            const parts = vmId.split("/");
            const resourceGroupName = parts[4];
            const vmName = parts[8];

            await this.networkClient.virtualMachines.beginDeallocateAndWait(resourceGroupName, vmName);
            console.log(`Azure: VM ${vmName} deallocated (stopped).`);
            return true;
        } catch (error) {
            console.error(`Azure: Failed to deallocate VM ${vmId}:`, error.message);
            return false;
        }
    }

    async listServers() {
        const initialized = await this.initClients();
        if (!initialized) {
            console.log("Azure: No credentials configured, skipping");
            return [];
        }
        try {
            const resourceGroups = await this.resourceClient.resourceGroups.list();
            const vms = [];
            for await (const rg of resourceGroups) {
                const virtualMachines = await this.computeClient.virtualMachines.list(rg.name);
                for await (const vm of virtualMachines) {
                    vms.push({
                        id: vm.id,
                        name: vm.name,
                        provider: this.name,
                        resourceGroup: rg.name,
                        vmSize: vm.hardwareProfile?.vmSize || 'unknown',
                        powerState: vm.instanceView?.statuses?.find(s => s.code?.startsWith('PowerState/'))?.displayStatus || 'unknown'
                    });
                }
            }
            return vms;
        } catch (error) {
            console.error("Azure: Failed to list VMs:", error.message);
            return [];
        }
    }
}

module.exports = AzureProvider;
