const axios = require('axios');
require('dotenv').config();

// Import all providers
const HetznerProvider = require('./providers/hetzner');
const VultrProvider = require('./providers/vultr');
const LinodeProvider = require('./providers/linode');
const DigitalOceanProvider = require('./providers/digitalocean');
const AWSLightsailProvider = require('./providers/aws-lightsail');
const AzureProvider = require('./providers/azure');

async function quickCheck() {
    console.log('⚡ Quick Bandwidth Check');
    console.log('========================');
    console.log('');

    const providers = [
        {
            name: 'Azure',
            provider: new AzureProvider(
                process.env.AZURE_CLIENT_ID,
                process.env.AZURE_CLIENT_SECRET,
                process.env.AZURE_TENANT_ID,
                process.env.AZURE_SUBSCRIPTION_ID
            ),
            configured: !!(process.env.AZURE_CLIENT_ID && process.env.AZURE_CLIENT_SECRET),
            expected: 'Should show ~1GB usage'
        },
        {
            name: 'DigitalOcean',
            provider: new DigitalOceanProvider(process.env.DIGITALOCEAN_TOKEN),
            configured: !!process.env.DIGITALOCEAN_TOKEN,
            expected: 'May show usage now or soon'
        },
        {
            name: 'Hetzner',
            provider: new HetznerProvider(process.env.HCLOUD_TOKEN),
            configured: !!process.env.HCLOUD_TOKEN,
            expected: 'Should update within 5-10 min'
        },
        {
            name: 'Linode',
            provider: new LinodeProvider(process.env.LINODE_TOKEN),
            configured: !!process.env.LINODE_TOKEN,
            expected: 'May take 5-15 minutes'
        },
        {
            name: 'AWS Lightsail',
            provider: new AWSLightsailProvider(
                process.env.AWS_ACCESS_KEY_ID,
                process.env.AWS_SECRET_ACCESS_KEY,
                process.env.AWS_REGION || 'us-east-1'
            ),
            configured: !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY),
            expected: 'CloudWatch delay 5-15 min'
        },
        {
            name: 'Vultr',
            provider: new VultrProvider(process.env.VULTR_API_KEY),
            configured: !!process.env.VULTR_API_KEY,
            expected: 'Often slowest (10-20 min)'
        }
    ];

    for (const { name, provider, configured, expected } of providers) {
        if (!configured) {
            console.log(`❌ ${name}: Not configured`);
            continue;
        }

        try {
            const servers = await provider.listServers();
            
            if (servers.length === 0) {
                console.log(`⚠️  ${name}: No servers found`);
                continue;
            }

            for (const server of servers) {
                try {
                    let usage;
                    
                    if (name === 'AWS Lightsail' && server.region) {
                        usage = await provider.getBandwidthUsage(server.id, server.region);
                    } else {
                        usage = await provider.getBandwidthUsage(server.id);
                    }
                    
                    if (usage) {
                        const usageGB = usage.outgoingBytes / (1024 ** 3);
                        const usageMB = usageGB * 1024;
                        
                        if (usageGB > 0.001) { // More than 1MB
                            console.log(`🎉 ${name}: ${usageMB.toFixed(2)} MB detected! ✅`);
                        } else if (usageGB > 0) {
                            console.log(`🔍 ${name}: ${usageMB.toFixed(2)} MB (small amount detected)`);
                        } else {
                            console.log(`⏳ ${name}: 0 MB (${expected})`);
                        }
                    } else {
                        console.log(`❌ ${name}: Error getting data`);
                    }
                } catch (error) {
                    console.log(`❌ ${name}: ${error.message}`);
                }
            }
        } catch (error) {
            console.log(`❌ ${name}: Provider error`);
        }
    }

    console.log('');
    console.log('💡 Tips:');
    console.log('');
    console.log('1. Azure is fastest - should show ~1GB usage');
    console.log('2. If still 0 everywhere, try downloading again:');
    console.log('   curl -o /dev/null http://speedtest.tele2.net/1GB.zip');
    console.log('3. Run this script again in 5-10 minutes');
    console.log('4. Use "node real-time-monitoring.js" for continuous monitoring');
}

quickCheck().catch(console.error);
