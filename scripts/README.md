# Multi-Cloud Bandwidth Monitor - Advanced Auto-Shutdown and Notifications

A sophisticated monitoring tool that tracks bandwidth usage across multiple cloud providers with advanced burst detection, configurable thresholds, and automatic instance termination to prevent excessive bandwidth charges.

## 🚀 Key Features

- **Multi-Level Monitoring**: Early warnings, burst detection, and monthly limits
- **Smart Burst Detection**: Allows temporary spikes while catching sustained abuse
- **Safety Mode**: Test configuration without actually terminating instances
- **Persistent Tracking**: Historical usage data and monthly projections
- **Multi-Provider Support**: Monitor all major cloud providers simultaneously
- **Flexible Notifications**: Slack and Discord webhook integration

## Supported Cloud Providers

- **Hetzner Cloud** ✅ Full bandwidth monitoring and auto-shutdown
- **Vultr** ✅ Full bandwidth monitoring and auto-shutdown
- **Linode** ✅ Full bandwidth monitoring and auto-shutdown
- **DigitalOcean** ✅ Full bandwidth monitoring and auto-shutdown
- **Google Cloud Platform (GCP)** ✅ Full bandwidth monitoring via Cloud Monitoring API
- **AWS Lightsail** ⚠️ Instance management (bandwidth monitoring requires CloudWatch setup)
- **Microsoft Azure** ⚠️ Instance management (bandwidth monitoring requires Azure Monitor setup)

## How It Works

### 🟡 **Tier 1: Early Warning**
- Triggers when daily usage exceeds the early warning threshold
- Sends notifications but takes no action
- Helps you monitor normal usage patterns

### 🟠 **Tier 2: Burst Detection**
- Triggers when daily usage exceeds the burst limit
- Allows temporary spikes within a configurable time window
- Records burst events for pattern analysis
- Escalates to termination if bursts become sustained

### 🔴 **Tier 3: Termination**
- Triggers on sustained burst usage or monthly limit projection
- Automatically terminates instances to prevent overage charges
- Can be disabled for testing (safety mode)
- Provides detailed logging of all actions taken

### 📊 **Advanced Features**
- **Historical Tracking**: Persistent storage of usage patterns in `bandwidth-usage.json`
- **Monthly Projections**: Estimates end-of-month usage based on current trends
- **Burst Window Analysis**: Distinguishes between legitimate spikes and abuse
- **Multi-Provider Coordination**: Monitors all cloud providers simultaneously
- **Safety Mode**: Test configuration without actually terminating instances

## ⚠️ Important Safety Notes

- **Default Safety Mode**: Instance termination is DISABLED by default (`ENABLE_INSTANCE_TERMINATION=false`)
- **Test First**: Always test with low thresholds and safety mode enabled
- **Understand Implications**: This tool can terminate your instances - configure carefully
- **Billing Lag**: Cloud provider billing may lag, so this might not fully prevent excess usage
- **Run Externally**: Better to run as a GitHub Action than on cloud instances themselves

## 🚀 Quick Start

### 1. Setup
```bash
cd scripts
npm install
cp .env.example .env
```

### 2. Configure API Credentials
Edit `.env` and add your cloud provider API tokens (see [API Setup Guide](#api-setup-guide) below).

### 3. Configure Monitoring Thresholds
Choose your configuration based on your needs:

#### 🧪 **Testing Configuration** (Recommended First)
```bash
# In .env file
MONTHLY_BANDWIDTH_LIMIT_GB=20480      # 20TB monthly limit
DAILY_BURST_LIMIT_GB=10               # 10GB daily burst (low for testing)
BURST_WINDOW_HOURS=6                  # 6 hour burst window (short for testing)
EARLY_WARNING_THRESHOLD_GB=5          # 5GB early warning (low for testing)
ENABLE_INSTANCE_TERMINATION=false     # SAFETY MODE - no actual termination
```

#### 🏭 **Production Configuration**
```bash
# In .env file
MONTHLY_BANDWIDTH_LIMIT_GB=20480      # 20TB monthly limit
DAILY_BURST_LIMIT_GB=1000             # 1TB daily burst allowance
BURST_WINDOW_HOURS=24                 # 24 hour burst window
EARLY_WARNING_THRESHOLD_GB=500        # 500GB early warning
ENABLE_INSTANCE_TERMINATION=true      # ENABLE actual termination
```

### 4. Test the System
```bash
# Run monitoring (safe mode by default)
npm start

# Or run the legacy version
npm run start-legacy
```

### 5. Generate Test Traffic (Optional)
```bash
# On your VPS to generate bandwidth usage
curl -o /dev/null http://speedtest.tele2.net/100MB.zip
```

## 📊 Understanding the Output

The system provides detailed analysis for each instance:

```
=== GCP: pangolin-test-792984 Analysis ===
Current usage: 2.58 GB
Daily burst limit: 10 GB
Monthly limit: 20480 GB
Action: warn
Reason: Daily burst limit exceeded but within burst window
Warnings: Daily usage (2.58 GB) exceeds early warning threshold (2 GB)
Termination enabled: false
```

## Environment Variables

### 🔑 **Advanced Bandwidth Monitoring** (Primary Configuration)

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `MONTHLY_BANDWIDTH_LIMIT_GB` | Monthly bandwidth limit in GB | 20480 | `20480` (20TB) |
| `DAILY_BURST_LIMIT_GB` | Daily burst allowance in GB | 1000 | `1000` (1TB) |
| `BURST_WINDOW_HOURS` | Hours to allow burst before termination | 24 | `24` |
| `EARLY_WARNING_THRESHOLD_GB` | Early warning threshold in GB | 500 | `500` |
| `ENABLE_INSTANCE_TERMINATION` | Enable actual termination (safety) | false | `true`/`false` |

### 🌐 **Cloud Provider Credentials**

Configure only the providers you want to monitor:

| Variable | Description | Required |
|----------|-------------|----------|
| `HCLOUD_TOKEN` | Hetzner Cloud API token | Optional |
| `VULTR_API_KEY` | Vultr API key | Optional |
| `LINODE_TOKEN` | Linode API token | Optional |
| `DIGITALOCEAN_TOKEN` | DigitalOcean API token | Optional |
| `AWS_ACCESS_KEY_ID` | AWS access key ID | Optional |
| `AWS_SECRET_ACCESS_KEY` | AWS secret access key | Optional |
| `AWS_REGION` | AWS region | Optional |
| `AZURE_CLIENT_ID` | Azure client ID | Optional |
| `AZURE_CLIENT_SECRET` | Azure client secret | Optional |
| `AZURE_TENANT_ID` | Azure tenant ID | Optional |
| `AZURE_SUBSCRIPTION_ID` | Azure subscription ID | Optional |
| `GCP_PROJECT_ID` | Google Cloud project ID | Optional |
| `GCP_CREDENTIALS_FILE` | Path to GCP service account key file | Optional |

### 📢 **Discord Notification Settings**

| Variable | Description | Required |
|----------|-------------|----------|
| `DISCORD_BOT_TOKEN` | Discord bot token for sending notifications | Optional |
| `DISCORD_CHANNEL_ID` | Discord channel ID where notifications will be sent | Optional |

### 📊 **Legacy Settings** (Deprecated)

These are kept for backward compatibility but are overridden by advanced settings:

| Variable | Description | Default |
|----------|-------------|---------|
| `NOTIFICATION_THRESHOLD_PERCENT` | Legacy percentage threshold | 80 |
| `KILL_THRESHOLD_PERCENT` | Legacy kill threshold | 95 |
| `SEND_USAGE_NOTIF_ALWAYS` | Always send notifications | false |
| `OBFUSCATE_SERVER_NAMES` | Hide server names | false |

## 📋 Configuration Examples

### Testing Configuration
Perfect for testing the system without risk:

```bash
# Advanced Bandwidth Monitoring
MONTHLY_BANDWIDTH_LIMIT_GB=20480      # 20TB monthly limit
DAILY_BURST_LIMIT_GB=5                # 5GB daily burst (low for testing)
BURST_WINDOW_HOURS=2                  # 2 hour burst window (short for testing)
EARLY_WARNING_THRESHOLD_GB=2          # 2GB early warning (low for testing)
ENABLE_INSTANCE_TERMINATION=false     # SAFETY MODE - no actual termination

# Notifications
SEND_USAGE_NOTIF_ALWAYS=true          # Always send reports for testing
```

### Production Configuration
For live monitoring with real protection:

```bash
# Advanced Bandwidth Monitoring
MONTHLY_BANDWIDTH_LIMIT_GB=20480      # 20TB monthly limit
DAILY_BURST_LIMIT_GB=1000             # 1TB daily burst allowance
BURST_WINDOW_HOURS=24                 # 24 hour burst window
EARLY_WARNING_THRESHOLD_GB=500        # 500GB early warning
ENABLE_INSTANCE_TERMINATION=true      # ENABLE actual termination

# Notifications
SEND_USAGE_NOTIF_ALWAYS=false         # Only send alerts when needed
```

### Conservative Configuration
For very strict bandwidth control:

```bash
# Advanced Bandwidth Monitoring
MONTHLY_BANDWIDTH_LIMIT_GB=10240      # 10TB monthly limit
DAILY_BURST_LIMIT_GB=500              # 500GB daily burst allowance
BURST_WINDOW_HOURS=12                 # 12 hour burst window
EARLY_WARNING_THRESHOLD_GB=200        # 200GB early warning
ENABLE_INSTANCE_TERMINATION=true      # ENABLE actual termination
```

## Limitations
Github will stop running the action if it's not used for 60 days. I think they'll send you an email to reactivate. This is another solution I came across that might help (and I might eventually integrate directly) https://github.com/marketplace/actions/keepalive-workflow.

## Setup Instructions

### 1. Fork Repository and Enable GitHub Actions

1. Fork this repository by clicking the "Fork" button at the top right of this page
2. Go to your forked repository 
3. Navigate to the "Actions" tab
4. Click the "I understand my workflows, go ahead and enable them" button to enable GitHub Actions

## 🔧 API Setup Guide

### Hetzner Cloud
1. Log in to [Hetzner Cloud Console](https://console.hetzner.cloud/)
2. Navigate to **Security → API Tokens**
3. Create a new API token with **read and write permissions**
4. Copy the token to `HCLOUD_TOKEN` in your `.env` file

### Vultr
1. Log in to [Vultr Account](https://my.vultr.com/)
2. Navigate to **Account → API**
3. Create a new API key with appropriate permissions
4. Copy the key to `VULTR_API_KEY` in your `.env` file

### Linode
1. Log in to [Linode Cloud Manager](https://cloud.linode.com/)
2. Navigate to **My Profile → API Tokens**
3. Create a new personal access token with **Linodes read/write permissions**
4. Copy the token to `LINODE_TOKEN` in your `.env` file

### DigitalOcean
1. Log in to [DigitalOcean Control Panel](https://cloud.digitalocean.com/)
2. Navigate to **API → Tokens/Keys**
3. Generate a new personal access token with **read and write scopes**
4. Copy the token to `DIGITALOCEAN_TOKEN` in your `.env` file

### Google Cloud Platform
1. Enable the **Cloud Monitoring API** for your project:
   - Visit: `https://console.developers.google.com/apis/api/monitoring.googleapis.com/overview?project=YOUR_PROJECT_ID`
   - Click "Enable"
2. Create a service account:
   - Navigate to **IAM & Admin → Service Accounts**
   - Create a new service account with **Compute Engine Admin** and **Monitoring Viewer** roles
3. Download the service account key file (JSON)
4. Set `GCP_PROJECT_ID` and `GCP_CREDENTIALS_FILE` in your `.env` file

### AWS Lightsail
1. Create an IAM user with **EC2** and **CloudWatch** permissions
2. Generate access keys for the user
3. Set `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, and `AWS_REGION` in your `.env` file

### Microsoft Azure
1. Create a service principal with appropriate permissions
2. Note the client ID, client secret, tenant ID, and subscription ID
3. Set `AZURE_CLIENT_ID`, `AZURE_CLIENT_SECRET`, `AZURE_TENANT_ID`, and `AZURE_SUBSCRIPTION_ID` in your `.env` file

### Discord Notifications (Optional)
1. **Create a Discord Application:**
   - Go to [Discord Developer Portal](https://discord.com/developers/applications)
   - Click "New Application" and give it a name
   - Go to the "Bot" section and click "Add Bot"
   - Copy the bot token and set it as `DISCORD_BOT_TOKEN` in your `.env` file

2. **Get Channel ID:**
   - In Discord, enable Developer Mode (User Settings → Advanced → Developer Mode)
   - Right-click on the channel where you want notifications
   - Click "Copy ID" and set it as `DISCORD_CHANNEL_ID` in your `.env` file

3. **Invite Bot to Server:**
   - In the Discord Developer Portal, go to OAuth2 → URL Generator
   - Select "bot" scope and "Send Messages" permission
   - Use the generated URL to invite the bot to your server

### 3. Create a Slack Webhook (Optional)

1. Create a new Slack app at [api.slack.com/apps](https://api.slack.com/apps)
2. Enable Incoming Webhooks
3. Create a new webhook URL for your workspace
4. Copy the webhook URL (you'll need it for GitHub secrets)

[Slack Webhooks Docs](https://api.slack.com/messaging/webhooks)

### 4. Set Up GitHub Repository Secrets and Environment Variables

1. Go to your repository's Settings → Secrets and variables → Actions → New repository secret
2. Add the following repository secrets:
   - `HETZNER_API_TOKEN`: Your Hetzner API token
   - `SLACK_WEBHOOK_URL`: Your Slack webhook URL (optional)
3. You can customize the monitoring thresholds by adding the following environment variables (Repository settings -> Actions -> Variables -> New repository variable):
- `THRESHOLD_PERCENT_NOTIF`: Percentage of bandwidth usage that triggers a notification (default: 50)
- `THRESHOLD_PERCENT_KILL`: Percentage of bandwidth usage that triggers server shutdown (default: 90)
- `SEND_USAGE_NOTIF_ALWAYS`: Set to 'true' to always send usage reports to Slack (default: false)
- `OBFUSCATE_SERVER_NAMES_FROM_CONSOLE_LOG`: Set to 'true' to hide server names in console output for privacy (default: false)

If you don't want it to kill, set `THRESHOLD_PERCENT_KILL` to a high value. Numbers can be set > 100 if you're OK with some overages.


### 5. Modify GitHub Cron Job

By default, this runs every 20 minutes. You can customize this with the cron job in `monitor.yml`.

## Usage

### Automatic Monitoring

The GitHub Action runs automatically according to the schedule defined in the workflow file (every 20 minutes).

### Manual Monitoring

You can also trigger the workflow manually:
1. Go to the Actions tab in your GitHub repository
2. Select the "Hetzner Traffic Stats" workflow
3. Click "Run workflow"

## 🔍 Monitoring and Troubleshooting

### Understanding the Analysis Output

Each instance shows detailed analysis:

```
=== GCP: pangolin-test-792984 Analysis ===
Current usage: 2.58 GB                    # Current 24h usage
Daily burst limit: 10 GB                  # Your configured limit
Monthly limit: 20480 GB                   # Your monthly limit
Action: warn                               # none/warn/terminate
Reason: Daily burst limit exceeded        # Why action was taken
Warnings: Daily usage exceeds threshold   # All warnings
Termination enabled: false                # Safety mode status
```

### Monitoring Files

The system creates several files for tracking:

- **`bandwidth-usage.json`**: Historical usage data and burst events
- **`package.json`**: Dependencies and scripts
- **`.env`**: Your configuration (keep private!)

### Testing Traffic Generation

Generate bandwidth usage for testing:

```bash
# On your VPS - download 100MB file
curl -o /dev/null http://speedtest.tele2.net/100MB.zip

# Download 1GB file (use carefully!)
curl -o /dev/null http://speedtest.tele2.net/1GB.zip

# Monitor the results
npm start
```

### Common Issues

**GCP "Cloud Monitoring API not enabled"**
```
Solution: Enable the API at:
https://console.developers.google.com/apis/api/monitoring.googleapis.com/overview?project=YOUR_PROJECT_ID
```

**"No instances found"**
```
Check: API credentials are correct and instances exist
Check: Instances are in running state
Check: API tokens have sufficient permissions
```

**"Would have terminated" messages**
```
This is normal in safety mode (ENABLE_INSTANCE_TERMINATION=false)
Set to true only when you're ready for actual termination
```

## 🏃 Running the Monitor

### Local Execution
```bash
cd scripts
npm start                    # Run advanced monitoring
npm run start-legacy        # Run legacy version
```

### Scheduled Execution
Set up cron job for regular monitoring:
```bash
# Run every hour
0 * * * * cd /path/to/scripts && npm start

# Run every 15 minutes (more aggressive)
*/15 * * * * cd /path/to/scripts && npm start
```

### GitHub Actions
Better to run externally to avoid terminating the monitoring system itself.

## 📁 Project Structure

```
scripts/
├── monitor.js              # Main monitoring script
├── monitor-refactored.js   # Alternative main script
├── bandwidth-tracker.js   # Advanced tracking logic
├── notification.js         # Notification service
├── utils.js               # Utility functions
├── providers/             # Cloud provider modules
│   ├── hetzner.js
│   ├── vultr.js
│   ├── linode.js
│   ├── aws-lightsail.js
│   ├── azure.js
│   ├── gcp.js
│   └── digitalocean.js
├── package.json
├── .env.example           # Configuration template
├── .env                   # Your configuration (private)
└── bandwidth-usage.json   # Historical data (auto-generated)
```

## ⚠️ Safety and Best Practices

### 🛡️ **Always Test First**
1. **Start with Safety Mode**: Keep `ENABLE_INSTANCE_TERMINATION=false` initially
2. **Use Low Thresholds**: Test with small values to see how the system behaves
3. **Monitor Logs**: Watch the detailed analysis output to understand decisions
4. **Verify Notifications**: Ensure alerts are working before enabling termination

### 🎯 **Recommended Workflow**
1. **Week 1**: Run in safety mode with low thresholds, monitor behavior
2. **Week 2**: Adjust thresholds based on your actual usage patterns
3. **Week 3**: Enable termination for non-critical instances first
4. **Week 4**: Full deployment with all instances

### 🔒 **Security Considerations**
- **Keep `.env` Private**: Never commit API credentials to version control
- **Use Read-Only Tokens**: Where possible, use tokens with minimal required permissions
- **Monitor Access**: Regularly review who has access to your monitoring system
- **Backup Configurations**: Keep copies of working configurations

### 📈 **Optimization Tips**
- **Adjust Burst Windows**: Longer windows for legitimate traffic spikes
- **Set Realistic Limits**: Base monthly limits on actual business needs
- **Monitor Patterns**: Use historical data to refine thresholds
- **Test Regularly**: Periodically verify the system with controlled traffic

### 🚨 **Emergency Procedures**
If the system terminates instances unexpectedly:
1. **Disable Termination**: Set `ENABLE_INSTANCE_TERMINATION=false`
2. **Check Logs**: Review the analysis output for termination reasons
3. **Verify Usage**: Confirm actual bandwidth usage with provider dashboards
4. **Adjust Thresholds**: Increase limits if termination was premature
5. **Restart Instances**: Manually restart any incorrectly terminated instances

## 🤝 Contributing

Feel free to submit issues and enhancement requests! Areas for contribution:
- Additional cloud provider support
- Enhanced notification formats
- Better usage prediction algorithms
- Web dashboard interface

## Disclaimer
This script is provided as-is and without warranty. Use it at your own risk!

## License

[MIT License](LICENSE)