const axios = require('axios');
require('dotenv').config();

const LINODE_TOKEN = process.env.LINODE_TOKEN;

async function debugLinodeDetailed() {
    console.log('🔍 Detailed Linode Bandwidth Debugging');
    console.log('======================================');
    console.log('Token configured:', !!LINODE_TOKEN);
    
    if (!LINODE_TOKEN) {
        console.error('❌ LINODE_TOKEN not configured');
        return;
    }

    try {
        // Step 1: List instances
        console.log('\n📋 Step 1: List Linode instances');
        const instancesRes = await axios.get('https://api.linode.com/v4/linode/instances', {
            headers: { Authorization: `Bearer ${LINODE_TOKEN}` }
        });
        
        console.log(`✅ Found ${instancesRes.data.data.length} instances`);
        
        if (instancesRes.data.data.length === 0) {
            console.log('❌ No instances found');
            return;
        }

        const instance = instancesRes.data.data[0];
        console.log(`📊 Testing instance: ${instance.label} (ID: ${instance.id})`);
        console.log(`📊 Status: ${instance.status}`);
        console.log(`📊 Type: ${instance.type}`);
        console.log(`📊 Region: ${instance.region}`);

        // Step 2: Get instance type details
        console.log('\n📋 Step 2: Get instance type details');
        const typeRes = await axios.get(`https://api.linode.com/v4/linode/types/${instance.type}`, {
            headers: { Authorization: `Bearer ${LINODE_TOKEN}` }
        });
        
        console.log(`✅ Transfer allowance: ${typeRes.data.transfer} GB`);
        console.log(`✅ Memory: ${typeRes.data.memory} MB`);
        console.log(`✅ VCPUs: ${typeRes.data.vcpus}`);

        // Step 3: Try different time ranges for stats
        console.log('\n📋 Step 3: Test stats endpoint with different time ranges');
        
        const timeRanges = [
            { name: 'Last hour', hours: 1 },
            { name: 'Last 6 hours', hours: 6 },
            { name: 'Last 24 hours', hours: 24 },
            { name: 'Last 7 days', hours: 24 * 7 }
        ];

        for (const range of timeRanges) {
            try {
                console.log(`\n🔍 Testing: ${range.name}`);
                const statsRes = await axios.get(`https://api.linode.com/v4/linode/instances/${instance.id}/stats`, {
                    headers: { Authorization: `Bearer ${LINODE_TOKEN}` }
                });

                if (statsRes.data.data && statsRes.data.data.network) {
                    const network = statsRes.data.data.network;
                    console.log(`✅ Network data available`);
                    console.log(`📊 Network out points: ${network.out ? network.out.length : 0}`);
                    console.log(`📊 Network in points: ${network.in ? network.in.length : 0}`);
                    
                    if (network.out && network.out.length > 0) {
                        console.log(`📊 Sample out data: ${JSON.stringify(network.out.slice(0, 3))}`);
                        
                        // Calculate total
                        const totalOut = network.out.reduce((sum, point) => sum + (point[1] || 0), 0);
                        console.log(`📊 Total outbound: ${totalOut} bytes (${(totalOut / (1024**3)).toFixed(6)} GB)`);
                    }
                } else {
                    console.log(`❌ No network data in stats`);
                }
            } catch (statsError) {
                console.log(`❌ Stats error: ${statsError.response?.data?.errors?.[0]?.reason || statsError.message}`);
            }
        }

        // Step 4: Try monthly transfer endpoint
        console.log('\n📋 Step 4: Test monthly transfer endpoint');
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1;
        
        try {
            const transferRes = await axios.get(
                `https://api.linode.com/v4/linode/instances/${instance.id}/transfer/${currentYear}/${currentMonth}`,
                { headers: { Authorization: `Bearer ${LINODE_TOKEN}` } }
            );
            
            console.log(`✅ Monthly transfer data available`);
            console.log(`📊 Used: ${transferRes.data.used} MB`);
            console.log(`📊 Quota: ${transferRes.data.quota} MB`);
            console.log(`📊 Billable: ${transferRes.data.billable} MB`);
            
            if (transferRes.data.used > 0) {
                console.log(`🎉 SUCCESS: Found ${transferRes.data.used} MB of transfer usage!`);
            }
        } catch (transferError) {
            console.log(`❌ Monthly transfer error: ${transferError.response?.data?.errors?.[0]?.reason || transferError.message}`);
        }

        // Step 5: Try previous month if current month shows 0
        console.log('\n📋 Step 5: Test previous month transfer');
        const prevMonth = currentMonth === 1 ? 12 : currentMonth - 1;
        const prevYear = currentMonth === 1 ? currentYear - 1 : currentYear;
        
        try {
            const prevTransferRes = await axios.get(
                `https://api.linode.com/v4/linode/instances/${instance.id}/transfer/${prevYear}/${prevMonth}`,
                { headers: { Authorization: `Bearer ${LINODE_TOKEN}` } }
            );
            
            console.log(`✅ Previous month transfer data available`);
            console.log(`📊 Used: ${prevTransferRes.data.used} MB`);
            
        } catch (prevTransferError) {
            console.log(`❌ Previous month error: ${prevTransferError.response?.data?.errors?.[0]?.reason || prevTransferError.message}`);
        }

        // Step 6: Check account transfer pool
        console.log('\n📋 Step 6: Check account transfer pool');
        try {
            const accountRes = await axios.get('https://api.linode.com/v4/account/transfer', {
                headers: { Authorization: `Bearer ${LINODE_TOKEN}` }
            });
            
            console.log(`✅ Account transfer data:`);
            console.log(`📊 Used: ${accountRes.data.used} GB`);
            console.log(`📊 Quota: ${accountRes.data.quota} GB`);
            console.log(`📊 Billable: ${accountRes.data.billable} GB`);
            
        } catch (accountError) {
            console.log(`❌ Account transfer error: ${accountError.message}`);
        }

        // Step 7: Manual traffic test suggestion
        console.log('\n📋 Step 7: Traffic generation test');
        console.log('💡 To test if metrics are working, SSH into your Linode and run:');
        console.log('');
        console.log('   # Generate outbound traffic');
        console.log('   curl -o /dev/null http://speedtest.tele2.net/100MB.zip');
        console.log('   curl -o /dev/null http://speedtest.tele2.net/1GB.zip');
        console.log('');
        console.log('   # Check if download completed successfully');
        console.log('   echo "Download completed at $(date)"');
        console.log('');
        console.log('   # Wait 10-15 minutes, then run this script again');

    } catch (error) {
        console.error('❌ Debug failed:', error.response?.data || error.message);
    }
}

debugLinodeDetailed();
