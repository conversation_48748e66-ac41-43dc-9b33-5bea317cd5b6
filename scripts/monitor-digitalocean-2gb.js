const axios = require('axios');
require('dotenv').config();

async function monitorDigitalOcean2GB() {
    console.log('🔍 DigitalOcean 2GB Download Test');
    console.log('=================================');
    console.log('');
    console.log('Monitoring DigitalOcean before and after your second 1GB download');
    console.log('Expected result: Should show ~2GB total after second download');
    console.log('');

    const DIGITALOCEAN_TOKEN = process.env.DIGITALOCEAN_TOKEN;
    
    if (!DIGITALOCEAN_TOKEN) {
        console.error('❌ DIGITALOCEAN_TOKEN not configured');
        return;
    }

    try {
        // Get droplet info
        const dropletsRes = await axios.get('https://api.digitalocean.com/v2/droplets', {
            headers: { Authorization: `Bearer ${DIGITALOCEAN_TOKEN}` }
        });
        
        if (dropletsRes.data.droplets.length === 0) {
            console.log('❌ No droplets found');
            return;
        }

        const droplet = dropletsRes.data.droplets[0];
        console.log(`📊 Droplet: ${droplet.name} (ID: ${droplet.id})`);
        console.log(`📊 Status: ${droplet.status}`);
        console.log(`📊 Size: ${droplet.size.slug}`);
        console.log('');

        // Function to check bandwidth
        async function checkBandwidth(label) {
            console.log(`🔍 ${label}`);
            console.log('=' .repeat(50));
            
            const now = Math.floor(Date.now() / 1000);
            const timeRanges = [
                { name: '1 hour', hours: 1 },
                { name: '6 hours', hours: 6 },
                { name: '24 hours', hours: 24 },
                { name: '48 hours', hours: 48 },
                { name: '7 days', hours: 24 * 7 }
            ];
            
            for (const range of timeRanges) {
                const start = now - (range.hours * 3600);
                
                // Check inbound (downloads)
                try {
                    const inRes = await axios.get(
                        `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=public&direction=inbound&start=${start}&end=${now}`,
                        { headers: { Authorization: `Bearer ${DIGITALOCEAN_TOKEN}` } }
                    );
                    
                    let inTotal = 0;
                    if (inRes.data.data && inRes.data.data.result) {
                        for (const result of inRes.data.data.result) {
                            if (result.values) {
                                for (const [timestamp, value] of result.values) {
                                    inTotal += parseFloat(value) * 300; // 5-minute intervals
                                }
                            }
                        }
                    }
                    
                    const inGB = inTotal / (1024**3);
                    console.log(`📥 ${range.name.padEnd(8)} inbound:  ${inGB.toFixed(6)} GB (${inTotal} bytes)`);
                    
                    if (inGB > 0.5) { // More than 500MB
                        console.log(`    🎉 Significant traffic detected!`);
                    }
                } catch (error) {
                    console.log(`❌ ${range.name} inbound error: ${error.message}`);
                }
                
                // Check outbound
                try {
                    const outRes = await axios.get(
                        `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=public&direction=outbound&start=${start}&end=${now}`,
                        { headers: { Authorization: `Bearer ${DIGITALOCEAN_TOKEN}` } }
                    );
                    
                    let outTotal = 0;
                    if (outRes.data.data && outRes.data.data.result) {
                        for (const result of outRes.data.data.result) {
                            if (result.values) {
                                for (const [timestamp, value] of result.values) {
                                    outTotal += parseFloat(value) * 300;
                                }
                            }
                        }
                    }
                    
                    const outGB = outTotal / (1024**3);
                    console.log(`📤 ${range.name.padEnd(8)} outbound: ${outGB.toFixed(6)} GB (${outTotal} bytes)`);
                } catch (error) {
                    console.log(`❌ ${range.name} outbound error: ${error.message}`);
                }
            }
            console.log('');
        }

        // Initial check
        await checkBandwidth('BEFORE Second Download');

        console.log('💡 INSTRUCTIONS:');
        console.log('================');
        console.log('');
        console.log('1. SSH into your DigitalOcean droplet:');
        console.log(`   ssh root@<digitalocean-ip>`);
        console.log('');
        console.log('2. Run the download command:');
        console.log('   curl -o /dev/null http://speedtest.tele2.net/1GB.zip');
        console.log('');
        console.log('3. Wait for download to complete');
        console.log('');
        console.log('4. Press Enter here to check again...');
        console.log('');

        // Wait for user input
        await new Promise(resolve => {
            process.stdin.once('data', () => resolve());
        });

        console.log('🔄 Checking bandwidth after second download...');
        console.log('');

        // Check again after download
        await checkBandwidth('AFTER Second Download');

        console.log('🎯 ANALYSIS:');
        console.log('============');
        console.log('');
        console.log('Expected results:');
        console.log('- BEFORE: ~1GB inbound (first download)');
        console.log('- AFTER: ~2GB inbound (both downloads)');
        console.log('');
        console.log('If still showing 0 or very small amounts:');
        console.log('1. DigitalOcean has very long metric delays (24-48+ hours)');
        console.log('2. Downloads might not be completing successfully');
        console.log('3. Different measurement methodology');
        console.log('');
        console.log('💡 Try running this script again in 1-2 hours to see if metrics update');

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

console.log('Press Ctrl+C to exit at any time');
monitorDigitalOcean2GB().catch(console.error);
