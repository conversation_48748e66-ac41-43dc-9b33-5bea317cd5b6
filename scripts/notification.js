const { Client, GatewayIntentBits, EmbedBuilder } = require('discord.js');

class NotificationService {
    constructor(discordBotToken, discordChannelId) {
        this.discordBotToken = discordBotToken;
        this.discordChannelId = discordChannelId;
        this.client = null;
        this.isReady = false;

        if (this.discordBotToken && this.discordChannelId) {
            this.initializeDiscord();
        }
    }

    async initializeDiscord() {
        try {
            this.client = new Client({
                intents: [GatewayIntentBits.Guilds]
            });

            this.client.once('ready', () => {
                console.log(`Discord bot logged in as ${this.client.user.tag}`);
                this.isReady = true;
            });

            await this.client.login(this.discordBotToken);
        } catch (error) {
            console.error('Failed to initialize Discord bot:', error.message);
            this.client = null;
        }
    }

    async send(message, type = "info") {
        if (!this.client || !this.isReady || !this.discordChannelId) {
            console.log("Discord not configured or not ready. Message only printed to console:");
            console.log(message);
            return;
        }

        let title = "📊 Bandwidth Monitor";
        let color = 0x36a64f; // Green for info

        if (type === "alert") {
            title = "⚠️ Bandwidth Alert";
            color = 0xFFA500; // Orange
        } else if (type === "kill") {
            title = "🚨 Server Shutdown Alert";
            color = 0xFF0000; // Red
        }

        try {
            const channel = await this.client.channels.fetch(this.discordChannelId);

            if (!channel) {
                console.error(`Discord channel ${this.discordChannelId} not found`);
                return;
            }

            const embed = new EmbedBuilder()
                .setTitle(title)
                .setDescription(message)
                .setColor(color)
                .setTimestamp()
                .setFooter({ text: 'Multi-Cloud Bandwidth Monitor' });

            await channel.send({ embeds: [embed] });
            console.log("Discord notification sent successfully.");

        } catch (error) {
            console.error("Error sending Discord message:", error.message);
            console.log("Message content:", message);
        }
    }

    async destroy() {
        if (this.client) {
            await this.client.destroy();
            console.log("Discord client destroyed.");
        }
    }
}

module.exports = NotificationService;
