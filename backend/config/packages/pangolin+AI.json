{"package": "Pangolin+AI", "package_description": "Secure MCP Gateway for AI Agentic Systems", "non_compatible_packages": ["Coolify", "Pangolin", "Pangolin+"], "components": ["pangolin+", "crowdsec", "middleware-manager", "m<PERSON><PERSON><PERSON>", "nlweb", "static-page", "traefik-log-dashboard"], "details": [{"name": "pangolin+", "required_env": [{"name": "DOMAIN", "hint": "Your domain name (e.g., example.com)"}, {"name": "EMAIL", "hint": "Email address for Let's Encrypt SSL certificates"}, {"name": "ADMIN_SUBDOMAIN", "hint": "Subdomain for admin access (e.g., pangolin)"}, {"name": "ADMIN_USERNAME", "hint": "Username for admin login"}, {"name": "ADMIN_PASSWORD", "hint": "Password for admin login (minimum 8 characters)"}], "optional_env": [{"name": "TRAEFIK_SUBDOMAIN", "hint": "Custom subdomain for Traefik dashboard (default: traefik)"}], "description": "Preconfigured tunnelled reverse proxy"}, {"name": "crowdsec", "required_env": [{"name": "CROWDSEC_ENROLLMENT_KEY", "hint": "CrowdSec enrollment key for security protection"}], "description": "Crowd sourced security protection"}, {"name": "m<PERSON><PERSON><PERSON>", "required_env": [{"name": "CLIENT_ID", "hint": "OAuth client ID for MCP authentication"}, {"name": "CLIENT_SECRET", "hint": "OAuth client secret for MCP authentication"}], "description": "Provides <PERSON><PERSON><PERSON> for MCP server"}, {"name": "middleware-manager", "required_env": [], "optional_env": [{"name": "MIDDLEWARE_MANAGER_SUBDOMAIN", "hint": "Custom subdomain for middleware manager (default: middleware)"}], "description": "A service that allows you to add custom middleware to Pangolin / Traefik resources"}, {"name": "nlweb", "required_env": [{"name": "OPENAI_API_KEY", "hint": "OpenAI API key for AI functionality"}], "optional_env": [{"name": "NLWEB_SUBDOMAIN", "hint": "Custom subdomain for Natural Language Web (default: nlweb)"}], "description": "Natural Language Web"}, {"name": "static-page", "required_env": [{"name": "STATIC_PAGE_SUBDOMAIN", "hint": "Subdomain for your static landing page (e.g., www)"}], "description": "Creates a static landing page and configures Traefik routing for it"}, {"name": "traefik-log-dashboard", "required_env": [{"name": "MAXMIND_LICENSE_KEY", "hint": "MaxMind license key for GeoIP functionality in logs"}], "optional_env": [{"name": "LOGS_SUBDOMAIN", "hint": "Custom subdomain for log dashboard (default: logs)"}], "description": "Enhanced Traefik log dashboard with OTLP support and GeoIP capabilities"}]}