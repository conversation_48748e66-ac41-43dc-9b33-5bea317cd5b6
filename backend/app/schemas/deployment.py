from pydantic import BaseModel
from datetime import datetime
from typing import Literal, Optional
from enum import Enum

class ServerType(str, Enum):
    new = "new"
    vps = "vps"

class DeploymentBase(BaseModel):
    package: Literal["Pangolin", "Pangolin+", "Pangolin+AI", "Coolify", "Coolify+"]
    # New server selection fields
    server_type: ServerType = ServerType.new
    vps_ip_address: str | None = None

    # Cloud fields (optional now; only required for server_type == new)
    cloud_provider: str | None = None
    region: str | None = None
    instance_type: str | None = None

    support_level: str
    komodo_provider_endpoint: str | None = None
    komodo_api_key: str | None = None
    komodo_api_secret: str | None = None
    github_token: str | None = None
    client_id: str | None = None
    client_name: str | None = None
    domain: str | None = None
    admin_email: str | None = None
    admin_username: str | None = None
    admin_password: str | None = None
    admin_subdomain: str | None = None
    postgres_user: str | None = None
    postgres_password: str | None = None
    postgres_host: str | None = None
    github_repo: str | None = None
    crowdsec_enrollment_key: str | None = None
    static_page_domain: str | None = None
    static_page_subdomain: str | None = None
    oauth_client_id: str | None = None
    oauth_client_secret: str | None = None
    openai_api_key: str | None = None
    komodo_host_ip: str | None = None
    komodo_passkey: str | None = None
    maxmind_license_key: str | None = None
    firewall_name: str | None = None
    instance_tags: list[str] | None = None
    instance_ip: str | None = None
    user_ssh_key: str | None = None

    # Optional environment variables
    traefik_subdomain: str | None = None
    middleware_manager_subdomain: str | None = None
    nlweb_subdomain: str | None = None
    logs_subdomain: str | None = None

class DeploymentCreate(DeploymentBase):
    pass

class DeploymentUpdate(BaseModel):
    package: str | None = None
    server_type: ServerType | None = None
    vps_ip_address: str | None = None
    cloud_provider: str | None = None
    region: str | None = None
    instance_type: str | None = None
    support_level: str | None = None
    komodo_provider_endpoint: str | None = None
    komodo_api_key: str | None = None
    komodo_api_secret: str | None = None
    github_token: str | None = None
    client_id: str | None = None
    client_name: str | None = None
    domain: str | None = None
    admin_email: str | None = None
    admin_username: str | None = None
    admin_password: str | None = None
    admin_subdomain: str | None = None
    postgres_user: str | None = None
    postgres_password: str | None = None
    postgres_host: str | None = None
    github_repo: str | None = None
    crowdsec_enrollment_key: str | None = None
    static_page_domain: str | None = None
    static_page_subdomain: str | None = None
    oauth_client_id: str | None = None
    oauth_client_secret: str | None = None
    openai_api_key: str | None = None
    komodo_host_ip: str | None = None
    komodo_passkey: str | None = None
    maxmind_license_key: str | None = None
    firewall_name: str | None = None
    instance_tags: list[str] | None = None
    instance_ip: str | None = None
    user_ssh_key: str | None = None

# Basic user info for deployment responses
class UserBasic(BaseModel):
    id: int
    username: str
    email: str

    class Config:
        from_attributes = True

class Deployment(DeploymentBase):
    id: int
    user_id: int | None = None  # Allow None for deleted user deployments
    cost: float  # hourly cost in cents (supports fractional cents)
    last_billed_at: datetime | None = None
    deleted_at: datetime | None = None
    created_at: datetime
    status: str
    failure_reason: str | None = None  # Store terraform error messages for failed deployments

    # User relationship for admin filtering
    user: Optional[UserBasic] = None

    class Config:
        from_attributes = True

    class Config:
        from_attributes = True
