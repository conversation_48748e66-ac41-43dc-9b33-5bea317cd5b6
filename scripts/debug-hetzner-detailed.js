const axios = require('axios');
require('dotenv').config();

const HETZNER_TOKEN = process.env.HCLOUD_TOKEN;

async function debugHetznerDetailed() {
    console.log('🔍 Detailed Hetzner Debugging');
    console.log('=============================');
    console.log('Token configured:', !!HETZNER_TOKEN);
    
    if (!HETZNER_TOKEN) {
        console.error('❌ HCLOUD_TOKEN not configured');
        return;
    }

    try {
        // Step 1: Get server details
        console.log('\n📋 Step 1: Get server details');
        const serversRes = await axios.get('https://api.hetzner.cloud/v1/servers', {
            headers: { Authorization: `Bearer ${HETZNER_TOKEN}` }
        });
        
        if (serversRes.data.servers.length === 0) {
            console.log('❌ No servers found');
            return;
        }

        const server = serversRes.data.servers[0];
        console.log(`📊 Server: ${server.name} (ID: ${server.id})`);
        console.log(`📊 Status: ${server.status}`);
        console.log(`📊 Server type: ${server.server_type.name}`);
        console.log(`📊 Outgoing traffic: ${server.outgoing_traffic} bytes`);
        console.log(`📊 Included traffic: ${server.included_traffic} bytes`);
        console.log(`📊 Outgoing traffic GB: ${(server.outgoing_traffic / (1024**3)).toFixed(6)} GB`);
        console.log(`📊 Included traffic GB: ${(server.included_traffic / (1024**3)).toFixed(6)} GB`);

        // Step 2: Get metrics data
        console.log('\n📋 Step 2: Get metrics data');
        const now = new Date();
        const oneDayAgo = new Date(now.getTime() - (24 * 60 * 60 * 1000));
        
        try {
            const metricsRes = await axios.get(`https://api.hetzner.cloud/v1/servers/${server.id}/metrics`, {
                headers: { Authorization: `Bearer ${HETZNER_TOKEN}` },
                params: {
                    type: 'network',
                    start: oneDayAgo.toISOString(),
                    end: now.toISOString()
                }
            });
            
            console.log(`✅ Metrics data available`);
            if (metricsRes.data.metrics && metricsRes.data.metrics.network) {
                const network = metricsRes.data.metrics.network;
                console.log(`📊 Network metrics available: ${Object.keys(network)}`);
                
                if (network.out) {
                    console.log(`📊 Network out values: ${network.out.values.length}`);
                    if (network.out.values.length > 0) {
                        console.log(`📊 Sample values:`, network.out.values.slice(0, 3));
                        
                        // Calculate total from metrics
                        const totalOut = network.out.values.reduce((sum, point) => {
                            return sum + parseFloat(point[1]);
                        }, 0);
                        console.log(`📊 Total from metrics: ${totalOut} bytes (${(totalOut / (1024**3)).toFixed(6)} GB)`);
                    }
                }
                
                if (network.in) {
                    console.log(`📊 Network in values: ${network.in.values.length}`);
                }
            }
        } catch (metricsError) {
            console.log(`❌ Metrics error: ${metricsError.message}`);
        }

        // Step 3: Compare different approaches
        console.log('\n📋 Step 3: Compare approaches');
        console.log('Current provider approach:');
        console.log(`  Outgoing: ${server.outgoing_traffic} bytes (${(server.outgoing_traffic / (1024**3)).toFixed(6)} GB)`);
        console.log(`  Included: ${server.included_traffic} bytes (${(server.included_traffic / (1024**3)).toFixed(6)} GB)`);
        
        if (server.outgoing_traffic > 0) {
            console.log(`🎉 SUCCESS: Hetzner server shows ${(server.outgoing_traffic / (1024**3)).toFixed(6)} GB outgoing traffic!`);
        } else {
            console.log(`⏳ No outgoing traffic detected yet`);
        }

        // Step 4: Check pricing info
        console.log('\n📋 Step 4: Server type pricing info');
        try {
            const serverTypeRes = await axios.get(`https://api.hetzner.cloud/v1/server_types/${server.server_type.id}`, {
                headers: { Authorization: `Bearer ${HETZNER_TOKEN}` }
            });
            
            console.log(`📊 Server type: ${serverTypeRes.data.server_type.name}`);
            console.log(`📊 Cores: ${serverTypeRes.data.server_type.cores}`);
            console.log(`📊 Memory: ${serverTypeRes.data.server_type.memory} GB`);
            console.log(`📊 Disk: ${serverTypeRes.data.server_type.disk} GB`);
            
        } catch (typeError) {
            console.log(`❌ Server type error: ${typeError.message}`);
        }

    } catch (error) {
        console.error('❌ Debug failed:', error.message);
    }
}

debugHetznerDetailed();
