const axios = require('axios');
require('dotenv').config();

async function investigate1GBDownloads() {
    console.log('🔍 Investigating 1GB Download Detection');
    console.log('======================================');
    console.log('');
    console.log('You downloaded 1GB on each VPS. Let\'s see where that data is...');
    console.log('');

    const expectedGB = 1.0;
    const tolerance = 0.1; // 100MB tolerance

    // Test each provider for both incoming and outgoing traffic
    const results = [];

    // 1. LINODE - Deep dive
    if (process.env.LINODE_TOKEN) {
        console.log('🔧 LINODE INVESTIGATION');
        console.log('=======================');
        try {
            // Get instances
            const instancesRes = await axios.get('https://api.linode.com/v4/linode/instances', {
                headers: { Authorization: `Bearer ${process.env.LINODE_TOKEN}` }
            });
            
            if (instancesRes.data.data.length > 0) {
                const instance = instancesRes.data.data[0];
                console.log(`📊 Instance: ${instance.label} (ID: ${instance.id})`);
                
                // Check account-level transfer
                try {
                    const accountRes = await axios.get('https://api.linode.com/v4/account/transfer', {
                        headers: { Authorization: `Bearer ${process.env.LINODE_TOKEN}` }
                    });
                    console.log(`📊 Account transfer used: ${accountRes.data.used} GB`);
                    results.push({
                        provider: 'Linode',
                        detected: accountRes.data.used,
                        expected: expectedGB,
                        status: Math.abs(accountRes.data.used - expectedGB) < tolerance ? '✅' : '❌'
                    });
                } catch (error) {
                    console.log(`❌ Account transfer error: ${error.message}`);
                }

                // Check monthly instance transfer
                const now = new Date();
                const year = now.getFullYear();
                const month = now.getMonth() + 1;
                
                try {
                    const monthlyRes = await axios.get(
                        `https://api.linode.com/v4/linode/instances/${instance.id}/transfer/${year}/${month}`,
                        { headers: { Authorization: `Bearer ${process.env.LINODE_TOKEN}` } }
                    );
                    console.log(`📊 Monthly instance transfer: ${monthlyRes.data.used} MB`);
                    if (monthlyRes.data.used) {
                        const usageGB = monthlyRes.data.used / 1024;
                        console.log(`📊 Monthly instance transfer: ${usageGB.toFixed(3)} GB`);
                    }
                } catch (error) {
                    console.log(`❌ Monthly transfer error: ${error.message}`);
                }
            }
        } catch (error) {
            console.log(`❌ Linode error: ${error.message}`);
        }
        console.log('');
    }

    // 2. HETZNER - Check both server stats and metrics
    if (process.env.HCLOUD_TOKEN) {
        console.log('🔧 HETZNER INVESTIGATION');
        console.log('========================');
        try {
            const serversRes = await axios.get('https://api.hetzner.cloud/v1/servers', {
                headers: { Authorization: `Bearer ${process.env.HCLOUD_TOKEN}` }
            });
            
            if (serversRes.data.servers.length > 0) {
                const server = serversRes.data.servers[0];
                console.log(`📊 Server: ${server.name} (ID: ${server.id})`);
                console.log(`📊 Outgoing traffic: ${server.outgoing_traffic} bytes (${(server.outgoing_traffic / (1024**3)).toFixed(6)} GB)`);
                
                // Check metrics for more detailed data
                try {
                    const now = new Date();
                    const start = new Date(now.getTime() - (24 * 60 * 60 * 1000));
                    
                    const metricsRes = await axios.get(`https://api.hetzner.cloud/v1/servers/${server.id}/metrics`, {
                        headers: { Authorization: `Bearer ${process.env.HCLOUD_TOKEN}` },
                        params: {
                            type: 'network',
                            start: start.toISOString(),
                            end: now.toISOString()
                        }
                    });
                    
                    if (metricsRes.data.metrics && metricsRes.data.metrics.network) {
                        const network = metricsRes.data.metrics.network;
                        if (network.out && network.out.values.length > 0) {
                            const totalOut = network.out.values.reduce((sum, point) => sum + parseFloat(point[1]), 0);
                            console.log(`📊 Metrics outbound: ${totalOut} bytes (${(totalOut / (1024**3)).toFixed(6)} GB)`);
                        }
                        if (network.in && network.in.values.length > 0) {
                            const totalIn = network.in.values.reduce((sum, point) => sum + parseFloat(point[1]), 0);
                            console.log(`📊 Metrics inbound: ${totalIn} bytes (${(totalIn / (1024**3)).toFixed(6)} GB)`);
                        }
                    }
                } catch (metricsError) {
                    console.log(`❌ Metrics error: ${metricsError.message}`);
                }
                
                const detectedGB = server.outgoing_traffic / (1024**3);
                results.push({
                    provider: 'Hetzner',
                    detected: detectedGB,
                    expected: expectedGB,
                    status: Math.abs(detectedGB - expectedGB) < tolerance ? '✅' : '❌'
                });
            }
        } catch (error) {
            console.log(`❌ Hetzner error: ${error.message}`);
        }
        console.log('');
    }

    // 3. DIGITALOCEAN - Check both directions and longer time periods
    if (process.env.DIGITALOCEAN_TOKEN) {
        console.log('🔧 DIGITALOCEAN INVESTIGATION');
        console.log('=============================');
        try {
            const dropletsRes = await axios.get('https://api.digitalocean.com/v2/droplets', {
                headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` }
            });
            
            if (dropletsRes.data.droplets.length > 0) {
                const droplet = dropletsRes.data.droplets[0];
                console.log(`📊 Droplet: ${droplet.name} (ID: ${droplet.id})`);
                
                const now = Math.floor(Date.now() / 1000);
                const timeRanges = [
                    { name: '24 hours', hours: 24 },
                    { name: '48 hours', hours: 48 },
                    { name: '72 hours', hours: 72 }
                ];
                
                for (const range of timeRanges) {
                    const start = now - (range.hours * 3600);
                    
                    // Check outbound
                    try {
                        const outRes = await axios.get(
                            `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=public&direction=outbound&start=${start}&end=${now}`,
                            { headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` } }
                        );
                        
                        let outTotal = 0;
                        if (outRes.data.data && outRes.data.data.result) {
                            for (const result of outRes.data.data.result) {
                                if (result.values) {
                                    for (const [timestamp, value] of result.values) {
                                        outTotal += parseFloat(value) * 300;
                                    }
                                }
                            }
                        }
                        console.log(`📤 ${range.name} outbound: ${(outTotal / (1024**3)).toFixed(6)} GB`);
                    } catch (error) {
                        console.log(`❌ ${range.name} outbound error: ${error.message}`);
                    }
                    
                    // Check inbound
                    try {
                        const inRes = await axios.get(
                            `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=public&direction=inbound&start=${start}&end=${now}`,
                            { headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` } }
                        );
                        
                        let inTotal = 0;
                        if (inRes.data.data && inRes.data.data.result) {
                            for (const result of inRes.data.data.result) {
                                if (result.values) {
                                    for (const [timestamp, value] of result.values) {
                                        inTotal += parseFloat(value) * 300;
                                    }
                                }
                            }
                        }
                        console.log(`📥 ${range.name} inbound: ${(inTotal / (1024**3)).toFixed(6)} GB`);
                        
                        if (inTotal > 500 * 1024 * 1024) { // More than 500MB
                            console.log(`🎉 Found significant inbound traffic in ${range.name}!`);
                        }
                    } catch (error) {
                        console.log(`❌ ${range.name} inbound error: ${error.message}`);
                    }
                }
            }
        } catch (error) {
            console.log(`❌ DigitalOcean error: ${error.message}`);
        }
        console.log('');
    }

    // 4. AWS LIGHTSAIL - Check both NetworkIn and NetworkOut
    if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
        console.log('🔧 AWS LIGHTSAIL INVESTIGATION');
        console.log('==============================');
        try {
            const { LightsailClient, GetInstancesCommand } = require("@aws-sdk/client-lightsail");
            const { CloudWatchClient, GetMetricStatisticsCommand } = require("@aws-sdk/client-cloudwatch");
            
            const lightsailClient = new LightsailClient({
                region: process.env.AWS_REGION || 'us-east-1',
                credentials: {
                    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
                }
            });
            
            const cloudwatchClient = new CloudWatchClient({
                region: process.env.AWS_REGION || 'us-east-1',
                credentials: {
                    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
                }
            });
            
            const instancesRes = await lightsailClient.send(new GetInstancesCommand({}));
            
            if (instancesRes.instances && instancesRes.instances.length > 0) {
                const instance = instancesRes.instances[0];
                console.log(`📊 Instance: ${instance.name}`);
                
                const now = new Date();
                const timeRanges = [
                    { name: '24 hours', hours: 24 },
                    { name: '48 hours', hours: 48 },
                    { name: '72 hours', hours: 72 }
                ];
                
                for (const range of timeRanges) {
                    const start = new Date(now.getTime() - (range.hours * 60 * 60 * 1000));
                    
                    // Check NetworkOut
                    try {
                        const outRes = await cloudwatchClient.send(new GetMetricStatisticsCommand({
                            Namespace: 'AWS/Lightsail',
                            MetricName: 'NetworkOut',
                            Dimensions: [{ Name: 'InstanceName', Value: instance.name }],
                            StartTime: start,
                            EndTime: now,
                            Period: 3600,
                            Statistics: ['Sum']
                        }));
                        
                        const outTotal = outRes.Datapoints ? outRes.Datapoints.reduce((sum, dp) => sum + (dp.Sum || 0), 0) : 0;
                        console.log(`📤 ${range.name} NetworkOut: ${(outTotal / (1024**3)).toFixed(6)} GB`);
                    } catch (error) {
                        console.log(`❌ ${range.name} NetworkOut error: ${error.message}`);
                    }
                    
                    // Check NetworkIn
                    try {
                        const inRes = await cloudwatchClient.send(new GetMetricStatisticsCommand({
                            Namespace: 'AWS/Lightsail',
                            MetricName: 'NetworkIn',
                            Dimensions: [{ Name: 'InstanceName', Value: instance.name }],
                            StartTime: start,
                            EndTime: now,
                            Period: 3600,
                            Statistics: ['Sum']
                        }));
                        
                        const inTotal = inRes.Datapoints ? inRes.Datapoints.reduce((sum, dp) => sum + (dp.Sum || 0), 0) : 0;
                        console.log(`📥 ${range.name} NetworkIn: ${(inTotal / (1024**3)).toFixed(6)} GB`);
                        
                        if (inTotal > 500 * 1024 * 1024) { // More than 500MB
                            console.log(`🎉 Found significant inbound traffic in ${range.name}!`);
                        }
                    } catch (error) {
                        console.log(`❌ ${range.name} NetworkIn error: ${error.message}`);
                    }
                }
            }
        } catch (error) {
            console.log(`❌ Lightsail error: ${error.message}`);
        }
        console.log('');
    }

    // Summary
    console.log('🎯 SUMMARY');
    console.log('==========');
    console.log('');
    console.log('Expected: ~1GB download on each VPS');
    console.log('');
    
    if (results.length > 0) {
        results.forEach(result => {
            console.log(`${result.status} ${result.provider}: ${result.detected.toFixed(3)} GB (expected: ${result.expected} GB)`);
        });
    }
    
    console.log('');
    console.log('💡 Key insights:');
    console.log('1. Downloads should appear as INCOMING/INBOUND traffic');
    console.log('2. Some providers have significant metric delays (24-72 hours)');
    console.log('3. Different providers measure traffic differently');
    console.log('4. Account-level vs instance-level tracking varies');
}

investigate1GBDownloads().catch(console.error);
