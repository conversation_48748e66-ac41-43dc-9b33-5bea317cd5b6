# Terraform configuration for Hetzner Cloud
terraform {
  required_providers {
    hcloud = {
      source = "hetznercloud/hcloud"
      version = "~> 1.0"
    }
    komodo-provider = {
      source = "registry.example.com/mattercoder/komodo-provider"
      version = ">= 1.0.0"
    }
  }
}

# Configure the Hetzner Cloud Provider
provider "hcloud" {
  token = var.hcloud_token
}

# SSH Key Resource
resource "hcloud_ssh_key" "default" {
  name       = "terraform-key-${var.client_name_lower}"
  public_key = var.ssh_public_key
}

# Create a server
resource "hcloud_server" "main" {
  name        = var.instance_name
  image       = var.os_type
  server_type = var.server_type
  location    = var.location
  ssh_keys    = [hcloud_ssh_key.default.id]
  user_data   = file("${path.module}/startup-script.sh")

  labels = {
    client = var.client_name_lower
  }
}

# Create a firewall if specified
resource "hcloud_firewall" "main" {
  count = var.create_firewall ? 1 : 0
  name  = "firewall-${var.client_name_lower}"

  # Allow SSH
  rule {
    direction  = "in"
    protocol   = "tcp"
    port       = "22"
    source_ips = var.allowed_source_ips
  }

  # Allow HTTP
  rule {
    direction  = "in"
    protocol   = "tcp"
    port       = "80"
    source_ips = var.allowed_source_ips
  }

  # Allow HTTPS
  rule {
    direction  = "in"
    protocol   = "tcp"
    port       = "443"
    source_ips = var.allowed_source_ips
  }

  # Allow Komodo API
  rule {
    direction  = "in"
    protocol   = "tcp"
    port       = "8120"
    source_ips = var.allowed_source_ips
  }

  # Allow Komodo UI
  rule {
    direction  = "in"
    protocol   = "tcp"
    port       = "9120"
    source_ips = var.allowed_source_ips
  }

  # Allow Newt Tunnel
  rule {
    direction  = "in"
    protocol   = "udp"
    port       = "51820"
    source_ips = var.allowed_source_ips
  }

  # Apply to the server
  apply_to {
    server = hcloud_server.main.id
  }
}

# Custom User Provider
provider "komodo-provider" {
  endpoint     = var.komodo_provider_endpoint
  api_key      = var.komodo_api_key
  api_secret   = var.komodo_api_secret
  github_token = var.github_token
}

# Custom provider resource with templated configuration
resource "komodo-provider_user" "client_syncresources" {
  depends_on = [hcloud_server.main]

  id           = var.client_id
  name         = var.client_name
  file_contents = templatefile("${path.module}/config-template.toml", {
    client_name_lower        = lower(var.client_name)
    client_name              = var.client_name
    domain                   = var.domain
    admin_email              = var.admin_email
    admin_username           = var.admin_username
    admin_password           = var.admin_password
    admin_subdomain          = var.admin_subdomain
    postgres_user            = var.postgres_user
    postgres_password        = var.postgres_password
    postgres_host            = var.postgres_host
    github_repo              = var.github_repo
    {% if package in ["Pangolin+", "Pangolin+AI"] %}
    crowdsec_enrollment_key = var.crowdsec_enrollment_key
    static_page_subdomain   = var.static_page_subdomain
    maxmind_license_key     = var.maxmind_license_key
    {% endif %}
    {% if package == "Pangolin+AI" %}
    oauth_client_id         = var.oauth_client_id
    oauth_client_secret     = var.oauth_client_secret
    komodo_passkey          = var.komodo_passkey
    openai_api_key          = var.openai_api_key
    {% endif %}
  })
  server_ip = hcloud_server.main.ipv4_address
}

# Output the server's IP address
output "server_ip" {
  value = hcloud_server.main.ipv4_address
}
