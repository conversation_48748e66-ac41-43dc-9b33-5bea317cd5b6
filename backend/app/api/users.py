from fastapi import APIRouter, Depends, HTTPException, Response, status, BackgroundTasks
from sqlalchemy.orm import Session
from app.schemas.user import User<PERSON><PERSON>, User, UserCreateAdmin
from app.db.session import get_db
from app.api.auth import get_current_user, get_admin_user
from app.crud.crud_user import (
    get_users as crud_get_users,
    get_user as crud_get_user,
    create_user as crud_create_user,
    get_user_by_email,
    make_user_admin,
    remove_admin_role,
    get_admin_users,
    prepare_user_for_deletion,
    get_user_deployments,
    deactivate_user
)
from app.models.deployment import Deployment as DeploymentModel
from app.core.deployment.deployment_manager import DeploymentManager
from typing import List
from app.services.email_service import send_verification_email
from datetime import datetime, timedelta

router = APIRouter()

# Admin-only: List all users
@router.get("/", response_model=List[User])
def read_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    users = crud_get_users(db, skip=skip, limit=limit)
    return users

# Public endpoint for user registration (first user becomes admin automatically)
@router.post("/", response_model=User)
def register(user: UserCreate, db: Session = Depends(get_db)):
    # Check for email conflicts
    db_user = get_user_by_email(db, email=user.email)
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")

    # Check for username conflicts
    from app.models.user import User as UserModel
    existing_username = db.query(UserModel).filter(UserModel.username == user.username).first()
    if existing_username:
        raise HTTPException(status_code=400, detail="Username already taken")

    return crud_create_user(db=db, user=user)

# Allow a user to resend their verification email
@router.post("/resend-verification")
def resend_verification(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    from app.models.user import User as UserModel
    db_user = db.query(UserModel).filter(UserModel.id == current_user.id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")
    if db_user.is_verified:
        return {"message": "Account already verified"}
    # Throttle resends to avoid abuse (e.g., 2 minutes)
    if db_user.verification_sent_at and (datetime.utcnow() - db_user.verification_sent_at).total_seconds() < 120:
        raise HTTPException(status_code=429, detail="Please wait before requesting another verification email")

    import secrets
    # Reuse existing valid token; otherwise generate new
    token = db_user.verification_token
    if not token or (db_user.verification_token_expires_at and db_user.verification_token_expires_at < datetime.utcnow()):
        token = secrets.token_urlsafe(32)
        db_user.verification_token = token
        db_user.verification_token_expires_at = datetime.utcnow() + timedelta(hours=24)
    # Update sent_at for throttling
    db_user.verification_sent_at = datetime.utcnow()
    db.add(db_user)
    db.commit()

    send_verification_email(db_user.email, db_user.username, token)
    return {"message": "Verification email sent"}

# Admin-only: Create new user with specified role
@router.post("/admin-create", response_model=User)
def create_user_as_admin(
    user: UserCreateAdmin,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    # Check for email conflicts
    db_user = get_user_by_email(db, email=user.email)
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")

    # Check for username conflicts
    from app.models.user import User as UserModel
    existing_username = db.query(UserModel).filter(UserModel.username == user.username).first()
    if existing_username:
        raise HTTPException(status_code=400, detail="Username already taken")

    return crud_create_user(db=db, user=UserCreate(
        username=user.username,
        email=user.email,
        password=user.password
    ), is_admin=user.is_admin)

# Admin can view any user, regular users can only view themselves
@router.get("/{user_id}", response_model=User)
def read_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # Check if user is admin or requesting their own profile
    if not current_user.is_admin and current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )

    db_user = crud_get_user(db, user_id=user_id)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return db_user

@router.get("/{user_id}/balance")
def get_user_balance(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get user's complete balance breakdown including credits.
    Users can only view their own balance, admins can view any user's balance.
    """
    # Check if user is admin or requesting their own balance
    if not current_user.is_admin and current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )

    db_user = crud_get_user(db, user_id=user_id)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")

    # Get complete balance breakdown
    from app.services.billing_service import BillingService
    billing_service = BillingService(db)
    balance_info = billing_service.get_user_total_balance(db_user)

    return {
        "user_id": user_id,
        "regular_balance": float(balance_info["regular_balance"]),
        "referral_credits": float(balance_info["referral_credits"]),
        "signup_credits": float(balance_info["signup_credits"]),
        "total_balance": float(balance_info["total_balance"])
    }

# Check self-deletion preconditions
@router.get("/self/deletion-check")
def check_self_deletion_preconditions_endpoint(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    from app.crud.crud_user import check_user_self_deletion_preconditions

    result = check_user_self_deletion_preconditions(db, current_user.id)
    if result is None:
        raise HTTPException(status_code=404, detail="User not found")

    return {
        "can_delete": result['can_delete'],
        "active_deployment_count": result['active_deployment_count'],
        "issues": result['issues'],
        "requirements": {
            "no_active_deployments": "You must deactivate all active deployments before deleting your account"
        }
    }

# User self-deletion (deactivates account for audit trail)
@router.delete("/self")
def self_delete_user_endpoint(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    from app.crud.crud_user import self_delete_user

    # Prevent deleting yourself if you're the last admin
    if current_user.is_admin:
        admin_count = len(get_admin_users(db))
        if admin_count <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete the last administrator"
            )

    result = self_delete_user(db, current_user.id)
    if result is None:
        raise HTTPException(status_code=404, detail="User not found")

    if not result['success']:
        # Preconditions not met - return detailed error
        check = result['precondition_check']
        raise HTTPException(
            status_code=400,
            detail=f"Cannot delete account: You have {check['active_deployment_count']} active deployment(s). Please deactivate all deployments first."
        )

    return {
        "message": f"Account deactivated successfully. Your account '{result['original_username']}' has been deactivated for audit purposes. Contact support if you need assistance.",
        "original_username": result['original_username'],
        "action": result['action']
    }


# Admin-only: Make user an administrator
@router.patch("/{user_id}/make-admin", response_model=User)
def make_admin(
    user_id: int,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    db_user = make_user_admin(db, user_id)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return db_user

# Admin-only: Remove admin role from user
@router.patch("/{user_id}/remove-admin", response_model=User)
def remove_admin(
    user_id: int,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    db_user = remove_admin_role(db, user_id)
    if db_user is None:
        raise HTTPException(status_code=400, detail="Cannot remove admin role - user not found or last admin")
    return db_user

# Admin-only: Prepare user for deletion (deactivate deployments and zero balance)
@router.post("/{user_id}/prepare-deletion")
def prepare_deletion(
    user_id: int,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    result = prepare_user_for_deletion(db, user_id)
    if result is None:
        raise HTTPException(status_code=404, detail="User not found")

    return {
        "message": f"User prepared for deletion. Deactivated {result['deactivated_deployments']} deployment(s) and zeroed balance.",
        "deactivated_deployments": result['deactivated_deployments'],
        "deployment_names": result['deployment_names']
    }

# Admin-only: Get user's deployments
@router.get("/{user_id}/deployments")
def get_user_deployments_endpoint(
    user_id: int,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    deployments = get_user_deployments(db, user_id)
    return [
        {
            "id": dep.id,
            "name": dep.name,
            "status": dep.status,
            "created_at": dep.created_at,
            "cost": dep.cost
        }
        for dep in deployments
    ]

# Admin-only: Check deactivation preconditions
@router.get("/{user_id}/deactivation-check")
def check_deactivation_preconditions_endpoint(
    user_id: int,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    from app.crud.crud_user import check_user_deactivation_preconditions

    result = check_user_deactivation_preconditions(db, user_id)
    if result is None:
        raise HTTPException(status_code=404, detail="User not found")

    return {
        "can_deactivate": result['can_deactivate'],
        "user": {
            "id": result['user'].id,
            "username": result['user'].username,
            "email": result['user'].email,
            "balance": result['balance']
        },
        "active_deployment_count": result['active_deployment_count'],
        "issues": result['issues'],
        "requirements": {
            "no_active_deployments": "User must have no active deployments (CREATING, PROVISIONING, ACTIVE)",
            "zero_or_negative_balance": "User balance must be zero or negative"
        }
    }

# Admin-only: Deactivate user account (only if preconditions are met)
@router.post("/{user_id}/deactivate")
def deactivate_user_endpoint(
    user_id: int,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    result = deactivate_user(db, user_id)
    if result is None:
        raise HTTPException(status_code=404, detail="User not found")

    if not result['success']:
        # Preconditions not met - return detailed error
        check = result['precondition_check']
        issues = []
        if check['issues']['has_active_deployments']:
            issues.append(f"{check['active_deployment_count']} active deployment(s)")
        if check['issues']['has_positive_balance']:
            issues.append(f"positive balance of €{check['balance']:.2f}")

        raise HTTPException(
            status_code=400,
            detail=f"Cannot deactivate user: {', '.join(issues)}. Please resolve these issues first."
        )

    return {
        "message": f"User account deactivated. Username changed from '{result['original_username']}' to 'deactivated_{user_id}'. Email '{result['original_email']}' preserved for identification.",
        "original_username": result['original_username'],
        "original_email": result['original_email']
    }
