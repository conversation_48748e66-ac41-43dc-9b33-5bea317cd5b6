const axios = require("axios");

class VultrProvider {
    constructor(apiToken) {
        this.apiToken = apiToken;
        this.name = "Vultr";
    }

    async getBandwidthUsage(instanceId) {
        try {
            const res = await axios.get(
                `https://api.vultr.com/v2/instances/${instanceId}/bandwidth`,
                {
                    headers: { Authorization: `Bearer ${this.apiToken}` },
                }
             );
            const bandwidthData = res.data.bandwidth;

            // Vultr returns bandwidth data by date
            let totalOutgoingBytes = 0;
            let totalIncomingBytes = 0;

            if (bandwidthData && typeof bandwidthData === 'object') {
                // Sum up all dates
                for (const [date, data] of Object.entries(bandwidthData)) {
                    if (data && typeof data === 'object') {
                        totalOutgoingBytes += (data.outgoing_bytes || 0);
                        totalIncomingBytes += (data.incoming_bytes || 0);
                    }
                }

                console.log(`Vultr: Found bandwidth data for ${Object.keys(bandwidthData).length} date(s)`);
                console.log(`Vultr: Total outgoing: ${totalOutgoingBytes} bytes (${(totalOutgoingBytes / (1024**3)).toFixed(6)} GB)`);
                console.log(`Vultr: Total incoming: ${totalIncomingBytes} bytes (${(totalIncomingBytes / (1024**3)).toFixed(6)} GB)`);

                // For bandwidth monitoring, we typically care about outgoing traffic
                // But downloads (like your 1GB test) show up as incoming traffic
                // We'll use outgoing as primary, but note incoming for reference
                const primaryBytes = totalOutgoingBytes;

                // Vultr doesn't have explicit transfer limits in the API response
                // Most plans have generous limits, using 1TB as default
                const includedBytes = 1000 * (1024 ** 3); // 1TB default

                return {
                    outgoingBytes: primaryBytes,
                    includedBytes: includedBytes,
                    // Store incoming for reference (downloads show up here)
                    incomingBytes: totalIncomingBytes
                };
            } else {
                console.log(`Vultr: No bandwidth data available for instance ${instanceId}`);
                return { outgoingBytes: 0, includedBytes: 1000 * (1024 ** 3) };
            }
        } catch (error) {
            console.error(`Vultr: Failed to get bandwidth for instance ${instanceId}:`, error.message);
            return null;
        }
    }

    async stopInstance(instanceId) {
        try {
            await axios.post(
                `https://api.vultr.com/v2/instances/${instanceId}/halt`,
                {},
                {
                    headers: { Authorization: `Bearer ${this.apiToken}` },
                }
             );
            console.log(`Vultr: Instance ${instanceId} halted.`);
            return true;
        } catch (error) {
            console.error(`Vultr: Failed to halt instance ${instanceId}:`, error.message);
            return false;
        }
    }

    async listServers() {
        if (!this.apiToken) {
            console.log("Vultr: No API token configured, skipping");
            return [];
        }
        try {
            const res = await axios.get("https://api.vultr.com/v2/instances", {
                headers: { Authorization: `Bearer ${this.apiToken}` },
            } );
            return res.data.instances.map((i) => ({
                id: i.id,
                name: i.label,
                provider: this.name,
            }));
        } catch (error) {
            console.error("Vultr: Failed to list instances:", error.message);
            return [];
        }
    }
}

module.exports = VultrProvider;
