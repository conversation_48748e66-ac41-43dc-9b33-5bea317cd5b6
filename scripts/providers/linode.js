const axios = require("axios");

class LinodeProvider {
    constructor(apiToken) {
        this.apiToken = apiToken;
        this.name = "Lin<PERSON>";
    }

    async getBandwidthUsage(linodeId) {
        try {
            // Get instance details to find the plan first
            const instanceDetails = await axios.get(
                `https://api.linode.com/v4/linode/instances/${linodeId}`,
                {
                    headers: { Authorization: `Bearer ${this.apiToken}` },
                }
             );
            const planId = instanceDetails.data.type;

            // Get plan details to find transfer limit
            const planDetails = await axios.get(
                `https://api.linode.com/v4/linode/types/${planId}`,
                {
                    headers: { Authorization: `Bearer ${this.apiToken}` },
                }
             );
            const includedBytes = planDetails.data.transfer * (1024 ** 3); // Transfer is in GB, convert to bytes

            // Try to get current month's transfer statistics first (more reliable)
            const now = new Date();
            const year = now.getFullYear();
            const month = now.getMonth() + 1; // JavaScript months are 0-indexed

            let outgoingBytes = 0;

            try {
                // Try monthly transfer endpoint first
                const transferRes = await axios.get(
                    `https://api.linode.com/v4/linode/instances/${linodeId}/transfer/${year}/${month}`,
                    {
                        headers: { Authorization: `Bearer ${this.apiToken}` },
                    }
                );

                if (transferRes.data && transferRes.data.used !== undefined && transferRes.data.used !== null) {
                    // Convert MB to bytes (Linode transfer API returns MB)
                    outgoingBytes = transferRes.data.used * 1024 * 1024;
                    console.log(`Linode: Got monthly transfer data for ${linodeId}: ${transferRes.data.used} MB`);
                } else {
                    console.log(`Linode: Monthly transfer data is undefined for ${linodeId}, trying account-level data...`);

                    // Try account-level transfer data as fallback
                    try {
                        const accountTransferRes = await axios.get(
                            'https://api.linode.com/v4/account/transfer',
                            {
                                headers: { Authorization: `Bearer ${this.apiToken}` },
                            }
                        );

                        if (accountTransferRes.data && accountTransferRes.data.used !== undefined) {
                            // Account transfer is in GB, convert to bytes
                            // Note: This is account-wide, not per-instance, but better than 0
                            outgoingBytes = accountTransferRes.data.used * 1024 * 1024 * 1024;
                            console.log(`Linode: Using account-level transfer data: ${accountTransferRes.data.used} GB (account-wide)`);
                        }
                    } catch (accountError) {
                        console.log(`Linode: Account transfer data also unavailable: ${accountError.message}`);
                    }
                }
            } catch (transferError) {
                console.log(`Linode: Monthly transfer data unavailable for ${linodeId}, trying stats endpoint...`);

                try {
                    // Fallback to stats endpoint
                    const statsRes = await axios.get(
                        `https://api.linode.com/v4/linode/instances/${linodeId}/stats`,
                        {
                            headers: { Authorization: `Bearer ${this.apiToken}` },
                        }
                    );

                    const networkStats = statsRes.data.data.network;
                    if (networkStats && networkStats.out && networkStats.out.length > 0) {
                        // Sum up all outgoing network data points for cumulative usage
                        outgoingBytes = networkStats.out.reduce((sum, dataPoint) => {
                            return sum + (dataPoint[1] || 0); // dataPoint[1] contains the bytes value
                        }, 0);
                        console.log(`Linode: Got stats data for ${linodeId}: ${outgoingBytes} bytes`);
                    }
                } catch (statsError) {
                    console.log(`Linode: Stats also unavailable for ${linodeId}:`, statsError.response?.data?.errors?.[0]?.reason || statsError.message);

                    // For new instances, return 0 usage but valid plan limits
                    if (statsError.response?.data?.errors?.[0]?.reason?.includes('unavailable')) {
                        console.log(`Linode: Instance ${linodeId} is new, returning 0 usage with plan limits`);
                        outgoingBytes = 0;
                    } else {
                        throw statsError;
                    }
                }
            }

            return { outgoingBytes, includedBytes };
        } catch (error) {
            console.error(`Linode: Failed to get bandwidth for Linode ${linodeId}:`, error.response?.data?.errors?.[0]?.reason || error.message);
            return null;
        }
    }

    async stopInstance(linodeId) {
        try {
            await axios.post(
                `https://api.linode.com/v4/linode/instances/${linodeId}/shutdown`,
                {},
                {
                    headers: { Authorization: `Bearer ${this.apiToken}` },
                }
             );
            console.log(`Linode: Linode ${linodeId} shut down.`);
            return true;
        } catch (error) {
            console.error(`Linode: Failed to shut down Linode ${linodeId}:`, error.message);
            return false;
        }
    }

    async listServers() {
        if (!this.apiToken) {
            console.log("Linode: No API token configured, skipping");
            return [];
        }
        try {
            const res = await axios.get("https://api.linode.com/v4/linode/instances", {
                headers: { Authorization: `Bearer ${this.apiToken}` },
            } );
            return res.data.data.map((l) => ({
                id: l.id,
                name: l.label,
                provider: this.name,
            }));
        } catch (error) {
            console.error("Linode: Failed to list Linodes:", error.message);
            return [];
        }
    }
}

module.exports = LinodeProvider;
