#!/bin/bash

echo "🔍 VPS Download Verification Script"
echo "==================================="
echo ""
echo "This script helps you verify that the 1GB downloads completed"
echo "successfully on each of your VPS instances."
echo ""

echo "📋 INSTRUCTIONS:"
echo "================"
echo ""
echo "For each VPS, SSH in and run the following commands to verify:"
echo ""

echo "1️⃣  HETZNER VPS (test-248445-instance)"
echo "   SSH command: ssh root@<hetzner-ip>"
echo "   Verification commands:"
echo "   ----------------------------------------"
echo "   # Check if curl is available"
echo "   which curl"
echo "   echo \"Curl version: \$(curl --version | head -1)\""
echo ""
echo "   # Test download with progress and verification"
echo "   echo \"Starting 100MB test download...\""
echo "   time curl -o /tmp/test-100mb.zip http://speedtest.tele2.net/100MB.zip"
echo "   ls -lh /tmp/test-100mb.zip"
echo "   echo \"100MB download completed at \$(date)\""
echo ""
echo "   # Test 1GB download with progress"
echo "   echo \"Starting 1GB download...\""
echo "   time curl -o /tmp/test-1gb.zip http://speedtest.tele2.net/1GB.zip"
echo "   ls -lh /tmp/test-1gb.zip"
echo "   echo \"1GB download completed at \$(date)\""
echo ""
echo "   # Clean up"
echo "   rm -f /tmp/test-*.zip"
echo "   ----------------------------------------"
echo ""

echo "2️⃣  DIGITALOCEAN VPS (pangolin-test-496320)"
echo "   SSH command: ssh root@<digitalocean-ip>"
echo "   Verification commands:"
echo "   ----------------------------------------"
echo "   # Check connectivity and curl"
echo "   ping -c 3 *******"
echo "   which curl"
echo ""
echo "   # Test download with detailed output"
echo "   echo \"Testing DigitalOcean download...\""
echo "   time curl -v -o /tmp/test-1gb.zip http://speedtest.tele2.net/1GB.zip"
echo "   ls -lh /tmp/test-1gb.zip"
echo "   echo \"Download completed at \$(date)\""
echo ""
echo "   # Check network interface stats"
echo "   cat /proc/net/dev"
echo "   ----------------------------------------"
echo ""

echo "3️⃣  VULTR VPS (test-429334-instance)"
echo "   SSH command: ssh root@<vultr-ip>"
echo "   Verification commands:"
echo "   ----------------------------------------"
echo "   # Vultr is working (1.01GB detected), but verify anyway"
echo "   echo \"Vultr verification (should already be working)...\""
echo "   time curl -o /tmp/test-1gb.zip http://speedtest.tele2.net/1GB.zip"
echo "   ls -lh /tmp/test-1gb.zip"
echo "   rm -f /tmp/test-1gb.zip"
echo "   ----------------------------------------"
echo ""

echo "4️⃣  AWS LIGHTSAIL VPS (pangolin-test-926632)"
echo "   SSH command: ssh ubuntu@<lightsail-ip> (or ssh ec2-user@<lightsail-ip>)"
echo "   Verification commands:"
echo "   ----------------------------------------"
echo "   # Check system and connectivity"
echo "   uname -a"
echo "   ping -c 3 *******"
echo "   which curl"
echo ""
echo "   # Test download"
echo "   echo \"Testing Lightsail download...\""
echo "   time curl -v -o /tmp/test-1gb.zip http://speedtest.tele2.net/1GB.zip"
echo "   ls -lh /tmp/test-1gb.zip"
echo "   echo \"Download completed at \$(date)\""
echo "   ----------------------------------------"
echo ""

echo "5️⃣  AZURE VPS (pangolin-test-761935)"
echo "   SSH command: ssh azureuser@<azure-ip>"
echo "   Verification commands:"
echo "   ----------------------------------------"
echo "   # Azure is working (1.48GB detected), but verify anyway"
echo "   echo \"Azure verification (should already be working)...\""
echo "   time curl -o /tmp/test-1gb.zip http://speedtest.tele2.net/1GB.zip"
echo "   ls -lh /tmp/test-1gb.zip"
echo "   rm -f /tmp/test-1gb.zip"
echo "   ----------------------------------------"
echo ""

echo "6️⃣  LINODE VPS (test-577516-instance)"
echo "   SSH command: ssh root@<linode-ip>"
echo "   Verification commands:"
echo "   ----------------------------------------"
echo "   # Linode is working (1.00GB detected), but verify anyway"
echo "   echo \"Linode verification (should already be working)...\""
echo "   time curl -o /tmp/test-1gb.zip http://speedtest.tele2.net/1GB.zip"
echo "   ls -lh /tmp/test-1gb.zip"
echo "   rm -f /tmp/test-1gb.zip"
echo "   ----------------------------------------"
echo ""

echo "🎯 WHAT TO LOOK FOR:"
echo "===================="
echo ""
echo "✅ Successful download indicators:"
echo "   - curl command completes without errors"
echo "   - File size is approximately 1,073,741,824 bytes (1GB)"
echo "   - Download time is reasonable (depends on connection)"
echo "   - No 'curl: (X) error' messages"
echo ""
echo "❌ Failed download indicators:"
echo "   - curl command fails with error"
echo "   - File size is 0 or much smaller than expected"
echo "   - Connection timeouts or DNS resolution errors"
echo "   - Permission denied or disk space errors"
echo ""

echo "🔧 TROUBLESHOOTING:"
echo "==================="
echo ""
echo "If downloads fail:"
echo "1. Check internet connectivity: ping *******"
echo "2. Check DNS resolution: nslookup speedtest.tele2.net"
echo "3. Try alternative download URLs:"
echo "   - http://speedtest.tele2.net/1GB.zip"
echo "   - http://ipv4.download.thinkbroadband.com/1GB.zip"
echo "   - https://ash-speed.hetzner.com/1GB.bin"
echo "4. Check disk space: df -h"
echo "5. Check firewall rules: iptables -L"
echo ""

echo "📊 AFTER VERIFICATION:"
echo "======================"
echo ""
echo "1. Wait 15-30 minutes after successful downloads"
echo "2. Run the monitoring script again:"
echo "   node test-all-providers.js"
echo "3. Check for updated bandwidth usage"
echo "4. If still showing 0, the provider may have longer delays (24-48 hours)"
echo ""

echo "💡 Remember: Downloads appear as INCOMING traffic, not outgoing!"
echo ""
