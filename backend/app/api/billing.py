from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Optional
from collections import defaultdict
from datetime import date
from pydantic import BaseModel
from decimal import Decimal

from app.db.session import get_db
from app.models.transaction import Transaction
from app.schemas.transaction import Transaction as TransactionSchema
from app.schemas.billing import BalanceAdjustment
from app.models.user import User
from app.api.auth import get_current_user, get_admin_user

router = APIRouter()

class GroupedTransactions(BaseModel):
    date: date
    transactions: List[TransactionSchema]


@router.get("/transactions", response_model=List[GroupedTransactions])
def read_user_transactions(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Retrieve and group transactions for the current user by date.
    """
    transactions = db.query(Transaction).filter(Transaction.user_id == current_user.id).order_by(Transaction.created_at.desc()).all()
    
    grouped: Dict[date, List[TransactionSchema]] = defaultdict(list)
    for tx in transactions:
        grouped[tx.created_at.date()].append(TransactionSchema.from_orm(tx))

    # Sort dates descending
    sorted_dates = sorted(grouped.keys(), reverse=True)

    response = [GroupedTransactions(date=day, transactions=grouped[day]) for day in sorted_dates]
    
    return response

@router.get("/admin/transactions", response_model=List[GroupedTransactions])
def read_admin_transactions(
    user: Optional[str] = Query(None, description="Filter by username"),
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user),
):
    """
    Retrieve and group transactions for admin view, optionally filtered by user.
    """
    query = db.query(Transaction)

    if user:
        # Join with User table to filter by username
        query = query.join(User).filter(User.username == user)

    transactions = query.order_by(Transaction.created_at.desc()).all()

    grouped: Dict[date, List[TransactionSchema]] = defaultdict(list)
    for tx in transactions:
        grouped[tx.created_at.date()].append(TransactionSchema.from_orm(tx))

    # Sort dates descending
    sorted_dates = sorted(grouped.keys(), reverse=True)

    response = [GroupedTransactions(date=day, transactions=grouped[day]) for day in sorted_dates]

    return response

@router.post("/admin/users/{user_id}/adjust-balance", response_model=TransactionSchema)
def adjust_user_balance(
    user_id: int,
    adjustment: BalanceAdjustment,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user),
):
    """
    Manually adjust a user's balance. Admin only.
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user.balance = (user.balance or Decimal("0")) + adjustment.amount

    tx = Transaction(
        user_id=user.id,
        amount=adjustment.amount,
        type="manual_adjustment",
        description=adjustment.description
    )

    db.add(user)
    db.add(tx)
    db.commit()
    db.refresh(tx)

    return tx
