const { CloudWatchClient, GetMetricDataCommand, ListMetricsCommand } = require("@aws-sdk/client-cloudwatch");
require('dotenv').config();

const AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID;
const AWS_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY;
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';

async function testCloudWatchPermissions() {
    console.log('🔍 Testing AWS CloudWatch Permissions...');
    console.log('Access Key configured:', !!AWS_ACCESS_KEY_ID);
    console.log('Secret Key configured:', !!AWS_SECRET_ACCESS_KEY);
    console.log('Region:', AWS_REGION);
    
    if (!AWS_ACCESS_KEY_ID || !AWS_SECRET_ACCESS_KEY) {
        console.error('❌ AWS credentials not configured');
        return;
    }

    try {
        const cloudwatchClient = new CloudWatchClient({
            region: AWS_REGION,
            credentials: {
                accessKeyId: AWS_ACCESS_KEY_ID,
                secretAccessKey: AWS_SECRET_ACCESS_KEY
            }
        });

        // Test 1: List available metrics
        console.log('\n📋 Test 1: List CloudWatch metrics');
        try {
            const listCommand = new ListMetricsCommand({
                Namespace: 'AWS/Lightsail',
                MetricName: 'NetworkOut'
            });
            const listData = await cloudwatchClient.send(listCommand);
            console.log('✅ ListMetrics permission: GRANTED');
            console.log(`📊 Found ${listData.Metrics?.length || 0} NetworkOut metrics for Lightsail`);
            
            if (listData.Metrics && listData.Metrics.length > 0) {
                console.log('📊 Available metrics:');
                listData.Metrics.forEach((metric, index) => {
                    console.log(`  ${index + 1}. ${metric.MetricName}`);
                    if (metric.Dimensions) {
                        metric.Dimensions.forEach(dim => {
                            console.log(`     ${dim.Name}: ${dim.Value}`);
                        });
                    }
                });
            }
        } catch (listError) {
            console.error('❌ ListMetrics permission: DENIED');
            console.error('Error:', listError.message);
        }

        // Test 2: Get metric data for a specific instance
        console.log('\n📊 Test 2: Get metric data');
        try {
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - (60 * 60 * 1000));

            const metricsCommand = new GetMetricDataCommand({
                MetricDataQueries: [
                    {
                        Id: 'networkOut',
                        MetricStat: {
                            Metric: {
                                Namespace: 'AWS/Lightsail',
                                MetricName: 'NetworkOut',
                                Dimensions: [
                                    {
                                        Name: 'InstanceName',
                                        Value: 'pangolin-test-926632' // Your instance name
                                    }
                                ]
                            },
                            Period: 3600, // 1 hour periods
                            Stat: 'Sum'
                        }
                    }
                ],
                StartTime: oneHourAgo,
                EndTime: now
            });

            const metricsData = await cloudwatchClient.send(metricsCommand);
            console.log('✅ GetMetricData permission: GRANTED');
            console.log('📊 Metric data response:', {
                resultsCount: metricsData.MetricDataResults?.length || 0,
                hasValues: metricsData.MetricDataResults?.[0]?.Values?.length > 0
            });

            if (metricsData.MetricDataResults && metricsData.MetricDataResults.length > 0) {
                const result = metricsData.MetricDataResults[0];
                console.log(`📊 Status: ${result.StatusCode}`);
                console.log(`📊 Values: ${result.Values?.length || 0} data points`);
                if (result.Values && result.Values.length > 0) {
                    console.log(`📊 Sample values: ${result.Values.slice(0, 3).join(', ')}`);
                } else {
                    console.log('📊 No data points available (normal for new instances)');
                }
            }

        } catch (metricsError) {
            console.error('❌ GetMetricData permission: DENIED');
            console.error('Error:', metricsError.message);
            
            if (metricsError.message.includes('not authorized')) {
                console.log('\n💡 To fix this, add the following policy to your IAM user:');
                console.log(JSON.stringify({
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Effect": "Allow",
                            "Action": [
                                "cloudwatch:GetMetricData",
                                "cloudwatch:GetMetricStatistics",
                                "cloudwatch:ListMetrics"
                            ],
                            "Resource": "*"
                        }
                    ]
                }, null, 2));
            }
        }

        // Test 3: Try alternative metric retrieval method
        console.log('\n🔄 Test 3: Alternative metric method');
        try {
            const { GetMetricStatisticsCommand } = require("@aws-sdk/client-cloudwatch");
            
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - (60 * 60 * 1000));

            const statsCommand = new GetMetricStatisticsCommand({
                Namespace: 'AWS/Lightsail',
                MetricName: 'NetworkOut',
                Dimensions: [
                    {
                        Name: 'InstanceName',
                        Value: 'pangolin-test-926632'
                    }
                ],
                StartTime: oneHourAgo,
                EndTime: now,
                Period: 3600,
                Statistics: ['Sum']
            });

            const statsData = await cloudwatchClient.send(statsCommand);
            console.log('✅ GetMetricStatistics permission: GRANTED');
            console.log(`📊 Datapoints: ${statsData.Datapoints?.length || 0}`);
            
            if (statsData.Datapoints && statsData.Datapoints.length > 0) {
                console.log('📊 Sample datapoint:', statsData.Datapoints[0]);
            }

        } catch (statsError) {
            console.error('❌ GetMetricStatistics permission: DENIED');
            console.error('Error:', statsError.message);
        }

    } catch (error) {
        console.error('❌ CloudWatch test failed:', error.message);
    }
}

testCloudWatchPermissions();
