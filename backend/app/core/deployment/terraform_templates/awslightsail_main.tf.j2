terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 6.4.0" # Use an appropriate version
    }
    komodo-provider = {
      source = "registry.example.com/mattercoder/komodo-provider"
      version = ">= 1.0.0"
    }
  }
}

provider "aws" {
  region = var.aws_region # You might need a more specific availability_zone for Lightsail
  access_key = var.aws_access_key
  secret_key = var.aws_secret_key
}

resource "aws_lightsail_key_pair" "default" {
  name       = "terraform-key-${var.client_name_lower}"
  public_key = var.ssh_public_key
}

resource "aws_lightsail_instance" "main" {
  name             = var.instance_name
  availability_zone = var.aws_availability_zone # e.g., "us-east-1a". Lightsail requires an AZ, not just a region.
  blueprint_id     = var.aws_blueprint_id      # e.g., "ubuntu_20_04" - find available blueprints via AWS CLI
  bundle_id        = var.aws_bundle_id         # e.g., "micro_2_0" - find available bundles (instance sizes) via AWS CLI
  key_pair_name    = aws_lightsail_key_pair.default.name
  user_data = file("${path.module}/startup-script.sh")
  tags = {
    ClientName = var.client_name_lower
  }
  # Lightsail instances typically get a public IP automatically.
}

resource "aws_lightsail_instance_public_ports" "web" {
  count = var.create_firewall ? 1 : 0
  instance_name = aws_lightsail_instance.main.name

  # Note: Lightsail handles firewall rules directly on the instance, primarily for inbound traffic.
  # Outbound rules are generally permissive by default.

  port_info {
    protocol   = "tcp"
    from_port  = 22
    to_port    = 22
    cidrs      = var.allowed_source_ips
  }
  port_info {
    protocol   = "tcp"
    from_port  = 80
    to_port    = 80
    cidrs      = ["0.0.0.0/0"] # equivalent to "0.0.0.0/0", "::/0" for IPv4
    ipv6_cidrs = ["::/0"]
  }
  port_info {
    protocol   = "tcp"
    from_port  = 443
    to_port    = 443
    cidrs      = ["0.0.0.0/0"]
    ipv6_cidrs = ["::/0"]
  }
  port_info {
    protocol   = "tcp"
    from_port  = 8120
    to_port    = 8120
    cidrs      = ["0.0.0.0/0"]
    ipv6_cidrs = ["::/0"]
  }
  port_info {
    protocol   = "tcp"
    from_port  = 9120
    to_port    = 9120
    cidrs      = ["0.0.0.0/0"]
    ipv6_cidrs = ["::/0"]
  }
  port_info { # Add WireGuard port if used
    protocol   = "udp"
    from_port  = 51820
    to_port    = 51820
    cidrs      = ["0.0.0.0/0"]
    ipv6_cidrs = ["::/0"]
  }
}

provider "komodo-provider" {
  endpoint     = var.komodo_provider_endpoint
  api_key      = var.komodo_api_key
  api_secret   = var.komodo_api_secret
  github_token = var.github_token
}

resource "komodo-provider_user" "client_syncresources" {
  depends_on = [aws_lightsail_instance.main]

  id            = var.client_id
  name          = var.client_name
  file_contents = templatefile("${path.module}/config-template.toml", {
    client_name_lower       = lower(var.client_name)
    client_name             = var.client_name
    domain                  = var.domain
    admin_email             = var.admin_email
    admin_username          = var.admin_username
    admin_password          = var.admin_password
    admin_subdomain         = var.admin_subdomain
    postgres_user           = var.postgres_user
    postgres_password       = var.postgres_password
    postgres_host           = var.postgres_host
    github_repo             = var.github_repo
    # The conditional block will work the same if 'package' variable is defined
    {% if package in ["Pangolin+", "Pangolin+AI"] %}
    crowdsec_enrollment_key = var.crowdsec_enrollment_key
    static_page_subdomain   = var.static_page_subdomain
    maxmind_license_key     = var.maxmind_license_key
    {% endif %}
    {% if package == "Pangolin+AI" %}
    oauth_client_id         = var.oauth_client_id
    oauth_client_secret     = var.oauth_client_secret
    komodo_passkey          = var.komodo_passkey
    openai_api_key          = var.openai_api_key
    {% endif %}
  })
  server_ip     = aws_lightsail_instance.main.public_ip_address # Use Lightsail's public IP
}