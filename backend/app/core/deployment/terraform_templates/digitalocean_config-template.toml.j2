[[stack]]
name = "{{ client_name_lower }}_setup-stack"
[stack.config]
server = "server-{{ client_name_lower }}"
repo = "{{ github_repo }}"
reclone = true
file_paths = ["docker-compose-setup.yml"]
environment = """
DOMAIN={{ domain }}
{% if package in ["Coolify", "Coolify+"] %}
ADMIN_SUBDOMAIN={{ admin_subdomain or "coolify" }}
{% else %}
EMAIL={{ admin_email }}
ADMIN_USERNAME={{ admin_username }}
ADMIN_PASSWORD={{ admin_password }}
ADMIN_SUBDOMAIN={{ admin_subdomain }}
{% if package not in ["Coolify", "Coolify+"] %}
{% if traefik_subdomain %}
TRAEFIK_SUBDOMAIN={{ traefik_subdomain }}
{% endif %}
{% if middleware_manager_subdomain %}
MIDDLEWARE_MANAGER_SUBDOMAIN={{ middleware_manager_subdomain }}
{% endif %}
{% if nlweb_subdomain %}
NLWEB_SUBDOMAIN={{ nlweb_subdomain }}
{% endif %}
{% if logs_subdomain %}
LOGS_SUBDOMAIN={{ logs_subdomain }}
{% endif %}
{% endif %}
{% endif %}
{% if package == "Pangolin" %}
COMPONENTS="pangolin"
{% elif package == "Pangolin+" %}
COMPONENTS="pangolin+,crowdsec,middleware-manager,static-page,traefik-log-dashboard"
{% elif package == "Pangolin+AI" %}
COMPONENTS="pangolin+,crowdsec,middleware-manager,komodo,mcpauth,nlweb,static-page,traefik-log-dashboard"
{% elif package == "Coolify" %}
COMPONENTS="coolify"
{% elif package == "Coolify+" %}
COMPONENTS="coolify+"
{% endif %}
{% if package in ["Pangolin+", "Pangolin+AI"] %}
CROWDSEC_ENROLLMENT_KEY={{ crowdsec_enrollment_key }}
STATIC_PAGE_SUBDOMAIN={{ static_page_subdomain }}
MAXMIND_LICENSE_KEY={{ maxmind_license_key }}
{% elif package == "Coolify+" %}
CROWDSEC_ENROLLMENT_KEY={{ crowdsec_enrollment_key }}
{% endif %}
{% if package == "Pangolin+AI" %}
CLIENT_ID={{ oauth_client_id }}
CLIENT_SECRET={{ oauth_client_secret }}
KOMODO_HOST_IP={{ komodo_host_ip }}
KOMODO_PASSKEY={{ komodo_passkey }}
OPENAI_API_KEY={{ openai_api_key }}
{% endif %}
"""

{% if package == "Coolify" %}
[[stack]]
name = "{{ client_name_lower }}_main-stack"
[stack.config]
server = "server-{{ client_name_lower }}"
files_on_host = true
reclone = true
run_directory = "/etc/komodo/stacks/{{ client_name_lower }}_setup-stack"
pre_deploy.command = """
# Add multiple commands on new lines. Supports comments.
cp ./config/coolify/proxy/dynamic/custom_domain.yml /data/coolify/proxy/dynamic/custom_domain.yml
"""
{% elif package == "Coolify+" %}
[[stack]]
name = "{{ client_name_lower }}_main-stack"
[stack.config]
server = "server-{{ client_name_lower }}"
files_on_host = true
reclone = true
run_directory = "/etc/komodo/stacks/{{ client_name_lower }}_setup-stack"
pre_deploy.command = """
# Add multiple commands on new lines. Supports comments.
cp ./config/coolify/proxy/dynamic/custom_domain.yml /data/coolify/proxy/dynamic/custom_domain.yml
cp ./config/coolify/proxy/docker-compose.override.yml /data/coolify/proxy/docker-compose.override.yml
"""
post_deploy.command = """
# Add multiple commands on new lines. Supports comments.
key=$(docker exec -i {{ client_name_lower }}_main-stack-crowdsec-1 cscli bouncers add traefik-bouncer 2>/dev/null | awk '/API key for/{getline; while($0 ~ /^$/){getline}; print $1}') && [ -n "$key" ] && sed -i "s|PASTE_YOUR_KEY_HERE|$key|" ./config/coolify/proxy/dynamic/crowdsec-plugin.yml
cp ./config/coolify/proxy/dynamic/crowdsec-plugin.yml /data/coolify/proxy/dynamic/crowdsec-plugin.yml
"""
{% else %}
[[stack]]
name = "{{ client_name_lower }}_main-stack"
[stack.config]
server = "server-{{ client_name_lower }}"
files_on_host = true
reclone = true
run_directory = "/etc/komodo/stacks/{{ client_name_lower }}_setup-stack"
{% if package in ["Pangolin+", "Pangolin+AI"] %}
post_deploy.command = """
# Add multiple commands on new lines. Supports comments.
docker exec {{ client_name_lower }}_main-stack-pangolin-1 pangctl set-admin-credentials --email "{{ admin_username }}"  --password "{{ admin_password }}"
chmod +x ./components/pangolin/initialize_sqlite.sh
./components/pangolin/initialize_sqlite.sh
chmod +x ./components/crowdsec/update-bouncer-post-install.sh
./components/crowdsec/update-bouncer-post-install.sh {{ client_name_lower }}_main-stack-crowdsec-1
"""
ignore_services = ["maxmind-updater"]
{% endif %}
{% endif %}

[[procedure]]
name = "{{ client_name }}_ProcedureApply"
description = "This procedure runs the initial setup that write out a compose file for the main stack deployment"

[[procedure.config.stage]]
name = "{{ client_name }}_Setup"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "{{ client_name_lower }}_setup-stack", execution.params.services = [], enabled = true }
]

[[procedure.config.stage]]
name = "Wait For Compose Write"
enabled = true
executions = [
  { execution.type = "Sleep", execution.params.duration_ms = 10000, enabled = true }
]

[[procedure.config.stage]]
name = "Destroy {{ client_name }}_Setup"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "{{ client_name_lower }}_setup-stack", execution.params.services = [], enabled = true }
]

[[procedure.config.stage]]
name = "{{ client_name }}_Stack"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "{{ client_name_lower }}_main-stack", execution.params.services = [], enabled = true }
]

[[procedure]]
name = "{{ client_name }}_ProcedureRestart"

[[procedure.config.stage]]
name = "Stop {{ client_name }}_Stack"
enabled = true
executions = [
  { execution.type = "StopStack", execution.params.stack = "{{ client_name_lower }}_main-stack", execution.params.services = [], enabled = true }
]

[[procedure.config.stage]]
name = "Wait For Stack Stop"
enabled = true
executions = [
  { execution.type = "Sleep", execution.params.duration_ms = 10000, enabled = true }
]

[[procedure.config.stage]]
name = "Start {{ client_name }}_Stack"
enabled = true
executions = [
  { execution.type = "StartStack", execution.params.stack = "{{ client_name_lower }}_main-stack", execution.params.services = [], enabled = true }
]

[[procedure]]
name = "{{ client_name }}_ProcedureDestroy"

[[procedure.config.stage]]
name = "{{ client_name }}_Stack"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "{{ client_name_lower }}_main-stack", execution.params.services = [], execution.params.remove_orphans = false, enabled = true }
]

[[procedure.config.stage]]
name = "{{ client_name }}_Setup"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "{{ client_name_lower }}_setup-stack", execution.params.services = [], execution.params.remove_orphans = false, enabled = true }
]

[[user_group]]
name = "{{ client_name }}_user_group"
permissions = [
  { target.type = "Server", target.id = "server-{{ client_name_lower }}", level = "Write", specific = ["Attach", "Inspect", "Logs", "Processes", "Terminal"] },
  { target.type = "Stack", target.id = "{{ client_name_lower }}_setup-stack", level = "Write", specific = ["Inspect", "Logs", "Terminal"] },
  { target.type = "Stack", target.id = "{{ client_name_lower }}_main-stack", level = "Write", specific = ["Inspect", "Logs", "Terminal"] }
]