const axios = require('axios');
require('dotenv').config();

// Import all providers
const HetznerProvider = require('./providers/hetzner');
const VultrProvider = require('./providers/vultr');
const LinodeProvider = require('./providers/linode');
const DigitalOceanProvider = require('./providers/digitalocean');
const AWSLightsailProvider = require('./providers/aws-lightsail');
const AzureProvider = require('./providers/azure');

async function diagnosticTest() {
    console.log('🔍 Traffic Generation Diagnostics');
    console.log('=================================');
    console.log('');
    console.log('This script helps diagnose why bandwidth usage might still show as 0');
    console.log('after generating traffic on your VPS instances.');
    console.log('');

    const providers = [
        {
            name: '<PERSON>tz<PERSON>',
            provider: new HetznerProvider(process.env.HCLOUD_TOKEN),
            configured: !!process.env.HCLOUD_TOKEN,
            expectedDelay: '5-10 minutes',
            metricsType: 'Hetzner Cloud API',
            notes: 'Usually fastest to update'
        },
        {
            name: 'Vultr',
            provider: new VultrProvider(process.env.VULTR_API_KEY),
            configured: !!process.env.VULTR_API_KEY,
            expectedDelay: '10-15 minutes',
            metricsType: 'Vultr API',
            notes: 'Can be slow to update'
        },
        {
            name: 'Linode',
            provider: new LinodeProvider(process.env.LINODE_TOKEN),
            configured: !!process.env.LINODE_TOKEN,
            expectedDelay: '5-15 minutes',
            metricsType: 'Linode API + Transfer endpoint',
            notes: 'May need stats to be available first'
        },
        {
            name: 'DigitalOcean',
            provider: new DigitalOceanProvider(process.env.DIGITALOCEAN_TOKEN),
            configured: !!process.env.DIGITALOCEAN_TOKEN,
            expectedDelay: '10-15 minutes',
            metricsType: 'DigitalOcean Monitoring API',
            notes: 'Metrics can be delayed'
        },
        {
            name: 'AWS Lightsail',
            provider: new AWSLightsailProvider(
                process.env.AWS_ACCESS_KEY_ID,
                process.env.AWS_SECRET_ACCESS_KEY,
                process.env.AWS_REGION || 'us-east-1'
            ),
            configured: !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY),
            expectedDelay: '5-15 minutes',
            metricsType: 'CloudWatch',
            notes: 'Depends on CloudWatch data availability'
        },
        {
            name: 'Azure',
            provider: new AzureProvider(
                process.env.AZURE_CLIENT_ID,
                process.env.AZURE_CLIENT_SECRET,
                process.env.AZURE_TENANT_ID,
                process.env.AZURE_SUBSCRIPTION_ID
            ),
            configured: !!(process.env.AZURE_CLIENT_ID && process.env.AZURE_CLIENT_SECRET),
            expectedDelay: '1-5 minutes',
            metricsType: 'Azure Monitor',
            notes: 'Usually updates quickly'
        }
    ];

    const currentTime = new Date();
    console.log(`🕐 Current time: ${currentTime.toISOString()}`);
    console.log('');

    for (const { name, provider, configured, expectedDelay, metricsType, notes } of providers) {
        console.log(`${'='.repeat(60)}`);
        console.log(`🔧 ${name} Diagnostics`);
        console.log(`${'='.repeat(60)}`);
        
        if (!configured) {
            console.log(`❌ ${name}: Not configured`);
            continue;
        }

        console.log(`📊 Expected delay: ${expectedDelay}`);
        console.log(`🔍 Metrics source: ${metricsType}`);
        console.log(`💡 Notes: ${notes}`);
        console.log('');

        try {
            const servers = await provider.listServers();
            
            if (servers.length === 0) {
                console.log(`⚠️  No servers found`);
                continue;
            }

            for (const server of servers) {
                console.log(`📋 Testing: ${server.name} (${server.id})`);
                
                try {
                    let usage;
                    
                    // Handle special cases
                    if (name === 'AWS Lightsail' && server.region) {
                        usage = await provider.getBandwidthUsage(server.id, server.region);
                    } else {
                        usage = await provider.getBandwidthUsage(server.id);
                    }
                    
                    if (usage) {
                        const usageGB = usage.outgoingBytes / (1024 ** 3);
                        const limitGB = usage.includedBytes / (1024 ** 3);
                        
                        console.log(`📈 Current usage: ${usageGB.toFixed(6)} GB`);
                        console.log(`📊 Transfer limit: ${limitGB.toFixed(0)} GB`);
                        console.log(`🔢 Raw bytes: ${usage.outgoingBytes}`);
                        
                        if (usageGB > 0) {
                            console.log(`🎉 SUCCESS: ${name} is detecting bandwidth usage!`);
                            console.log(`💾 Detected: ${(usageGB * 1024).toFixed(2)} MB`);
                        } else {
                            console.log(`⏳ WAITING: ${name} hasn't detected usage yet`);
                            console.log(`🕐 Expected update time: ${expectedDelay}`);
                            
                            // Provider-specific troubleshooting
                            if (name === 'Hetzner') {
                                console.log(`💡 Hetzner tip: Check if traffic is outbound (not just inbound)`);
                            } else if (name === 'Vultr') {
                                console.log(`💡 Vultr tip: Metrics can be very delayed, try again in 15-20 minutes`);
                            } else if (name === 'Linode') {
                                console.log(`💡 Linode tip: Stats may need to be enabled first`);
                            } else if (name === 'DigitalOcean') {
                                console.log(`💡 DigitalOcean tip: Monitoring metrics update every 5-10 minutes`);
                            } else if (name === 'AWS Lightsail') {
                                console.log(`💡 Lightsail tip: CloudWatch metrics may take 5-15 minutes`);
                            }
                        }
                    } else {
                        console.log(`❌ Failed to get bandwidth data`);
                    }
                } catch (error) {
                    console.log(`❌ Error: ${error.message}`);
                }
                
                console.log('');
            }
            
        } catch (error) {
            console.log(`❌ Provider error: ${error.message}`);
        }
        
        console.log('');
    }

    console.log(`${'='.repeat(60)}`);
    console.log('📋 Summary & Next Steps');
    console.log(`${'='.repeat(60)}`);
    console.log('');
    console.log('🎯 What to expect:');
    console.log('');
    console.log('1. Azure: Should show usage within 1-5 minutes ✅');
    console.log('2. Hetzner: Should show usage within 5-10 minutes');
    console.log('3. AWS Lightsail: Should show usage within 5-15 minutes');
    console.log('4. Linode: Should show usage within 5-15 minutes');
    console.log('5. DigitalOcean: Should show usage within 10-15 minutes');
    console.log('6. Vultr: Should show usage within 10-20 minutes');
    console.log('');
    console.log('⏰ If still showing 0 after expected time:');
    console.log('');
    console.log('1. Try generating more traffic:');
    console.log('   curl -o /dev/null http://speedtest.tele2.net/1GB.zip');
    console.log('');
    console.log('2. Check if the download actually completed successfully');
    console.log('');
    console.log('3. Some providers only count certain types of traffic');
    console.log('');
    console.log('4. Run this diagnostic again in 10-15 minutes');
    console.log('');
    console.log('💡 Remember: 1GB download should show as ~1.0 GB usage');
}

diagnosticTest().catch(console.error);
