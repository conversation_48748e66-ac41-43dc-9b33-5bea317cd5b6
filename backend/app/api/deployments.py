from fastapi import APIRouter, Depends, BackgroundTasks, HTTPException
from datetime import datetime
from sqlalchemy.orm import Session, joinedload
import json
import os

from pathlib import Path
from app.schemas.deployment import DeploymentCreate, Deployment, DeploymentBase, DeploymentUpdate, ServerType
from app.db.session import <PERSON><PERSON><PERSON><PERSON>
from app.models.deployment import Deployment as DeploymentModel
from app.models.user import User as UserModel
from app.core.deployment.deployment_manager import DeploymentManager
from app.api.auth import get_current_user, get_admin_user
from app.schemas.user import User
from app.core.pricing.pricing_service import PricingService
from app.core.config import get_settings
from app.services.billing_job import run_billing_for_user_deployment
from app.services.email_service import _send_email
from datetime import datetime
from app.services.dns_ssl_monitor import dns_ssl_checker

# Router without global API key dependency - individual endpoints handle their own auth
router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

async def _validate_package_compatibility(db: Session, package: str, vps_ip_address: str):
    """
    Validate that the package is compatible with existing deployments on the same server IP.
    Checks both vps_ip_address (BYOVPS) and instance_ip (cloud deployments) fields.
    Raises HTTPException if incompatible packages are found.
    """
    # Load package configuration to get non_compatible_packages
    package_file_mapping = {
        "Pangolin": "pangolin.json",
        "Pangolin+": "pangolin+.json",
        "Pangolin+AI": "pangolin+AI.json",
        "Coolify": "coolify.json",
        "Coolify+": "coolify+.json"
    }

    if package not in package_file_mapping:
        return  # Unknown package, skip validation

    # Load package config to get non_compatible_packages
    config_path = Path(__file__).parent.parent.parent / "config" / "packages" / package_file_mapping[package]
    if not config_path.exists():
        return  # Config file not found, skip validation

    try:
        with open(config_path, 'r') as f:
            package_config = json.load(f)

        non_compatible_packages = package_config.get("non_compatible_packages", [])
        if not non_compatible_packages:
            return  # No compatibility restrictions

        # Check for existing active deployments on the same IP address
        # Need to check both vps_ip_address (BYOVPS) and instance_ip (cloud deployments)
        from sqlalchemy import or_
        existing_deployments = db.query(DeploymentModel).filter(
            or_(
                DeploymentModel.vps_ip_address == vps_ip_address,
                DeploymentModel.instance_ip == vps_ip_address
            ),
            DeploymentModel.status.in_(["ACTIVE", "READY", "CREATING", "PROVISIONING", "DNS_FAILED", "DNS_VALIDATION"]),
            DeploymentModel.deleted_at.is_(None)
        ).all()

        # Check if any existing deployment has an incompatible package
        for existing_deployment in existing_deployments:
            if existing_deployment.package in non_compatible_packages:
                raise HTTPException(
                    status_code=400,
                    detail=f"Cannot install {package} on server {vps_ip_address}. "
                           f"The server already has {existing_deployment.package} installed, "
                           f"which is not compatible with {package}. "
                           f"Please use a different server or remove the existing deployment first."
                )

    except json.JSONDecodeError:
        # If config file is malformed, skip validation
        return

@router.get("/packages/")
def get_packages():
    """Get all available packages with their descriptions"""
    package_file_mapping = {
        "Pangolin": "pangolin.json",
        "Pangolin+": "pangolin+.json",
        "Pangolin+AI": "pangolin+AI.json",
        "Coolify": "coolify.json",
        "Coolify+": "coolify+.json"
    }

    packages = []
    for package_name, filename in package_file_mapping.items():
        # Try multiple possible locations for the package config files
        possible_paths = [
            Path(__file__).parent.parent.parent / "config" / "packages" / filename,
            Path(__file__).parent.parent / "config" / "packages" / filename,
        ]

        config_path = None
        for path in possible_paths:
            if os.path.exists(path):
                config_path = path
                break

        if config_path:
            try:
                with open(config_path, 'r') as file:
                    config = json.load(file)
                packages.append({
                    "name": package_name,
                    "display_name": config.get("package", package_name),
                    "description": config.get("package_description", ""),
                    "features": []  # Could be populated from components if needed
                })
            except Exception as e:
                # Fallback to basic package info if config can't be read
                packages.append({
                    "name": package_name,
                    "display_name": package_name,
                    "description": "",
                    "features": []
                })
        else:
            # Fallback if config file not found
            packages.append({
                "name": package_name,
                "display_name": package_name,
                "description": "",
                "features": []
            })

    return packages

@router.get("/packages/{package_name}/config")
def get_package_config(package_name: str):
    """Get the configuration for a specific package"""
    # Map package names to file names
    package_file_mapping = {
        "Pangolin": "pangolin.json",
        "Pangolin+": "pangolin+.json",
        "Pangolin+AI": "pangolin+AI.json",
        "Coolify": "coolify.json",
        "Coolify+": "coolify+.json"
    }

    if package_name not in package_file_mapping:
        raise HTTPException(status_code=404, detail="Package not found")

    # Try multiple possible locations for the package config files
    possible_paths = [
        Path(__file__).parent.parent.parent / "config" / "packages" / package_file_mapping[package_name],
        Path(__file__).parent.parent / "config" / "packages" / package_file_mapping[package_name],
    ]

    config_path = None
    for path in possible_paths:
        if os.path.exists(path):
            config_path = path
            break

    if not config_path:
        raise HTTPException(status_code=404, detail=f"Package configuration file not found for {package_name}")

    try:
        with open(config_path, 'r') as file:
            config = json.load(file)
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading package configuration: {str(e)}")


pricing_service = PricingService()

@router.get("/cloud-providers/")
def get_cloud_providers():
    # Filter pricing data by active status and exclude BYOVPS for new server deployments
    filtered_data = []
    for item in pricing_service.pricing_data:
        # Check if entry is active (default to active if not specified)
        is_active = item.get('active', '1') == '1'
        if not is_active:
            continue

        # Exclude BYOVPS from cloud provider options (BYOVPS is only for VPS server type)
        if item['cloud_provider'] == 'BYOVPS':
            continue

        filtered_data.append(item)

    # Extract unique providers from filtered data
    providers = set(item['cloud_provider'] for item in filtered_data)
    return [{"name": provider, "display_name": provider} for provider in providers]

@router.get("/regions/{provider}")
def get_regions(provider: str):
    # Filter pricing data by active status
    filtered_data = []
    for item in pricing_service.pricing_data:
        # Check if entry is active (default to active if not specified)
        is_active = item.get('active', '1') == '1'
        if not is_active:
            continue

        # Only include items for the specified provider
        if item['cloud_provider'] == provider:
            filtered_data.append(item)

    # Extract unique regions from filtered data with country codes
    regions_dict = {}
    for item in filtered_data:
        region = item['region']
        country_code = item.get('country_code', '')
        if region not in regions_dict:
            regions_dict[region] = country_code

    return [{"name": region, "display_name": region, "country_code": country_code}
            for region, country_code in regions_dict.items()]

@router.get("/instance-types/{provider}/{region}")
def get_instance_types(provider: str, region: str):
    # Filter pricing data by active status
    instance_types = []
    for item in pricing_service.pricing_data:
        # Check if entry is active (default to active if not specified)
        is_active = item.get('active', '1') == '1'
        if not is_active:
            continue

        # Only include items for the specified provider and region
        if item['cloud_provider'] == provider and item['region'] == region:
            instance_types.append({
                "name": item['instance_type'],
                "display_name": item['instance_type'],
                "cpu": float(item.get('cpu', 1)),  # Get CPU from pricing data
                "memory": float(item.get('memory', 1)),  # Get memory from pricing data
                "hourly_cost": float(item['hourly_cost'])
            })
    return instance_types

@router.post("/pricing/calculate", response_model=dict)
def calculate_price(deployment: DeploymentBase):
    """Calculate pricing based on deployment configuration using the pricing service"""
    pricing_result = pricing_service.calculate_pricing(
        cloud_provider=deployment.cloud_provider,
        instance_type=deployment.instance_type,
        region=deployment.region,
        package=deployment.package,
        support_level=deployment.support_level,
        server_type=deployment.server_type.value
    )

    return pricing_result

@router.post("/", response_model=Deployment)
async def create_deployment(deployment: DeploymentCreate, background_tasks: BackgroundTasks, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    settings = get_settings()
    # Validate based on server_type
    if deployment.server_type == ServerType.new:
        if not deployment.cloud_provider or not deployment.region or not deployment.instance_type:
            raise HTTPException(status_code=400, detail="cloud_provider, region and instance_type are required for new server deployments")
        if deployment.cloud_provider == "Hetzner":
            if not settings.HCLOUD_TOKEN:
                raise HTTPException(status_code=400, detail="Hetzner Cloud API token is missing in environment variables")
    elif deployment.server_type == ServerType.vps:
        if not deployment.vps_ip_address:
            raise HTTPException(status_code=400, detail="vps_ip_address is required for BYOVPS deployments")
        # Optional: perform a quick validation now or require prior validation endpoint

        # Check package compatibility for BYOVPS deployments
        await _validate_package_compatibility(db, deployment.package, deployment.vps_ip_address)

    cost_data = calculate_price(deployment)

    # Store hourly cost in cents for billing (round to handle fractional cents)
    hourly_cost_cents = round(cost_data['hourly_cost'] * 100, 4)

    # Enforce minimum balance requirement and verified account to create deployment
    min_balance = get_settings().MIN_BALANCE_TO_DEPLOY
    # refetch User model to check numeric balance and verification
    db_user = db.query(UserModel).filter(UserModel.id == current_user.id).first()
    if db_user is not None:
        if not db_user.is_verified:
            raise HTTPException(status_code=403, detail="Please verify your email address before creating deployments.")

        # Calculate total balance including referral and signup credits
        from app.services.referral_service import ReferralService
        from app.services.signup_credit_service import SignupCreditService

        referral_service = ReferralService(db)
        signup_service = SignupCreditService(db)

        regular_balance = float(db_user.balance or 0)
        referral_credits = float(referral_service.calculate_total_available_credit(db_user.id))
        signup_credits = float(signup_service.get_usable_signup_balance(db_user.id))
        total_balance = regular_balance + referral_credits + signup_credits

        if total_balance < min_balance:
            raise HTTPException(
                status_code=402,
                detail=f"Insufficient balance. Minimum {min_balance} EUR required to create a deployment. "
                       f"Current balance: {regular_balance:.2f} EUR + {referral_credits:.2f} EUR referral credits + "
                       f"{signup_credits:.2f} EUR signup credits = {total_balance:.2f} EUR total."
            )

    db_deployment = DeploymentModel(**deployment.dict(), user_id=current_user.id, cost=hourly_cost_cents, status='CREATING')
    db.add(db_deployment)
    db.commit()
    db.refresh(db_deployment)

    deployment_manager = DeploymentManager()
    background_tasks.add_task(deployment_manager.deploy, int(db_deployment.id))

    return db_deployment


@router.delete("/{deployment_id}")
async def delete_deployment(
    deployment_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    db_deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
    if not db_deployment:
        raise HTTPException(status_code=404, detail="Deployment not found")

    # Check ownership (admin can delete any, user can only delete their own)
    if not current_user.is_admin and db_deployment.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this deployment")

    # Allow permanent deletion of FAILED deployments and deployments that are already disabled (deleted_at is set)
    if db_deployment.status == 'FAILED' or db_deployment.deleted_at is not None:
        # Permanent deletion - remove from database completely
        deployment_manager = DeploymentManager()

        if db_deployment.status == 'FAILED':
            background_tasks.add_task(deployment_manager.cleanup_failed_deployment, int(db_deployment.id))
        elif db_deployment.deleted_at is not None:
            # Already disabled, just clean up any remaining resources
            background_tasks.add_task(deployment_manager.cleanup_failed_deployment, int(db_deployment.id))

        # Remove from database
        db.delete(db_deployment)
        db.commit()

        return {"message": "Deployment permanently deleted"}

    # Check if deployment is currently being processed (applies to all statuses)
    if db_deployment.status in ['RESTARTING', 'DESTROYING']:
        raise HTTPException(status_code=400, detail=f"Cannot delete deployment that is currently {db_deployment.status.lower()}")

    # For deployments with infrastructure (ACTIVE, READY, DNS_FAILED, DNS_VALIDATION), disable them (set deleted_at)
    elif db_deployment.status in ['ACTIVE', 'READY', 'DNS_FAILED', 'DNS_VALIDATION']:
        # Set status to DESTROYING immediately to prevent race conditions
        db_deployment.status = 'DESTROYING'
        db_deployment.deleted_at = datetime.now() # type: ignore
        db.add(db_deployment)
        db.commit()
        db.refresh(db_deployment)

        # Start the destroy process in background
        deployment_manager = DeploymentManager()
        background_tasks.add_task(deployment_manager.destroy, int(db_deployment.id))

        # Trigger billing refresh to calculate any final charges for this deployment
        background_tasks.add_task(run_billing_for_user_deployment, current_user.id, db_deployment.id)

        return db_deployment

    # For deployments in progress (CREATING, PROVISIONING), mark as failed and clean up
    elif db_deployment.status in ['CREATING', 'PROVISIONING']:
        # Mark as failed and clean up
        db_deployment.status = 'FAILED'
        db_deployment.deleted_at = datetime.now() # type: ignore
        db.add(db_deployment)
        db.commit()

        # Clean up any partial resources
        deployment_manager = DeploymentManager()
        background_tasks.add_task(deployment_manager.cleanup_failed_deployment, int(db_deployment.id))

        return db_deployment

    else:
        raise HTTPException(status_code=400, detail=f"Cannot delete deployment with status: {db_deployment.status}")





@router.get("/{deployment_id}/status", response_model=str)
async def get_deployment_status(
    deployment_id: int, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    db_deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
    if not db_deployment:
        raise HTTPException(status_code=404, detail="Deployment not found")
    
    # Check ownership (admin can view any, user can only view their own)
    if not current_user.is_admin and db_deployment.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to view this deployment")
    
    return db_deployment.status



@router.patch("/{deployment_id}", response_model=Deployment)
async def update_deployment(deployment_id: int, deployment_update: DeploymentUpdate, background_tasks: BackgroundTasks, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    db_deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
    if not db_deployment:
        raise HTTPException(status_code=404, detail="Deployment not found")
    
    # Add this ownership check
    if db_deployment.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this deployment")

    for key, value in deployment_update.dict(exclude_unset=True).items():
        setattr(db_deployment, key, value)

    db.add(db_deployment)
    db.commit()
    db.refresh(db_deployment)

    # deployment_manager = DeploymentManager()
    # background_tasks.add_task(deployment_manager.update, db_deployment)

    return db_deployment


@router.get("/", response_model=list[Deployment])
async def get_deployments(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    # Admin can see all deployments, regular users only see their own
    if current_user.is_admin:
        deployments = db.query(DeploymentModel).options(joinedload(DeploymentModel.user)).all()
    else:
        deployments = db.query(DeploymentModel).filter(DeploymentModel.user_id == current_user.id).all()
    return deployments

# Admin-only: Get deployments by specific user
@router.get("/user/{user_id}", response_model=list[Deployment])
async def get_deployments_by_user(
    user_id: int, 
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    deployments = db.query(DeploymentModel).filter(DeploymentModel.user_id == user_id).all()
    return deployments

@router.post("/billing/refresh", response_model=dict)
async def refresh_billing(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db), 
    current_user: User = Depends(get_current_user)
):
    """
    Manually trigger billing calculation for the current user's deployments.
    This ensures users see up-to-date billing calculations.
    """
    background_tasks.add_task(run_billing_for_user_deployment, current_user.id)
    return {"message": "Billing refresh initiated for user deployments"}

@router.post("/billing/refresh/{deployment_id}", response_model=dict)
async def refresh_billing_for_deployment(
    deployment_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Manually trigger billing calculation for a specific deployment.
    """
    # Verify deployment ownership
    db_deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
    if not db_deployment:
        raise HTTPException(status_code=404, detail="Deployment not found")

    if not current_user.is_admin and db_deployment.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this deployment")

    background_tasks.add_task(run_billing_for_user_deployment, current_user.id, deployment_id)
    return {"message": f"Billing refresh initiated for deployment {deployment_id}"}

@router.post("/pricing/reload", response_model=dict)
async def reload_pricing_data(current_user: User = Depends(get_admin_user)):
    """
    Reload pricing data from database (admin only).
    Useful after updating pricing.csv and running load_pricing_data.py
    """
    try:
        pricing_service.reload_pricing_data()
        return {"message": "Pricing data reloaded successfully", "total_entries": len(pricing_service.pricing_data)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reload pricing data: {str(e)}")


@router.get("/{deployment_id}/dns-ssl-status")
async def get_deployment_dns_ssl_status(
    deployment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get DNS and SSL status for a specific deployment"""
    db_deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
    if not db_deployment:
        raise HTTPException(status_code=404, detail="Deployment not found")

    # Check ownership (admin can view any, user can only view their own)
    if not current_user.is_admin and db_deployment.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to view this deployment")

    # Only monitor deployments that have infrastructure ready
    if db_deployment.status not in ['ACTIVE', 'DNS_VALIDATION', 'READY', 'DNS_FAILED']:
        raise HTTPException(status_code=400, detail="Can only monitor DNS/SSL for deployments with infrastructure ready")

    print(f"[DEBUG] Checking DNS/SSL for deployment {deployment_id}, status: {db_deployment.status}")
    print(f"[DEBUG] Deployment domain: {db_deployment.domain}, instance_ip: {db_deployment.instance_ip}")

    result = dns_ssl_checker.check_deployment_dns_ssl(db_deployment)
    print(f"[DEBUG] DNS/SSL check result: {result}")

    return result


@router.post("/{deployment_id}/restart")
async def restart_deployment(
    deployment_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Restart a deployment to fix SSL certificate issues"""
    print(f"[DEBUG] Restart request for deployment {deployment_id}")

    db_deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
    if not db_deployment:
        print(f"[DEBUG] Deployment {deployment_id} not found")
        raise HTTPException(status_code=404, detail="Deployment not found")

    print(f"[DEBUG] Deployment {deployment_id} status: {db_deployment.status}")

    # Check ownership (admin can restart any, user can only restart their own)
    if not current_user.is_admin and db_deployment.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to restart this deployment")

    # Only restart deployments with infrastructure ready
    if db_deployment.status not in ['ACTIVE', 'READY']:
        print(f"[DEBUG] Cannot restart deployment with status: {db_deployment.status}")
        raise HTTPException(status_code=400, detail=f"Can only restart deployments with infrastructure ready. Current status: {db_deployment.status}")

    # Prevent concurrent operations
    if db_deployment.status in ['RESTARTING', 'DESTROYING']:
        raise HTTPException(status_code=400, detail=f"Cannot restart deployment that is currently {db_deployment.status.lower()}")

    # Check if restart is recommended (temporarily disabled for testing)
    print(f"[DEBUG] Checking DNS/SSL status for restart recommendation...")
    try:
        monitoring_result = dns_ssl_checker.check_deployment_dns_ssl(db_deployment)
        print(f"[DEBUG] Monitoring result: {monitoring_result}")
        needs_restart = monitoring_result.get('needs_restart', False)
        print(f"[DEBUG] Needs restart: {needs_restart}")

        # Temporarily allow restart regardless of recommendation
        # if not needs_restart:
        #     raise HTTPException(status_code=400, detail="Deployment restart not recommended based on current status")
    except Exception as e:
        print(f"[DEBUG] Error checking DNS/SSL status: {e}")
        # Continue with restart even if monitoring check fails

    # Use deployment manager to restart (stop/start stack in Komodo)
    deployment_manager = DeploymentManager()

    # Schedule restart in background
    background_tasks.add_task(deployment_manager.restart, db_deployment.id)

    return {"message": f"Deployment {deployment_id} restart initiated"}


@router.post("/{deployment_id}/retry-dns-validation")
async def retry_dns_validation(
    deployment_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Retry DNS validation for a deployment with DNS_FAILED status"""
    db_deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
    if not db_deployment:
        raise HTTPException(status_code=404, detail="Deployment not found")

    # Check ownership (admin can retry any, user can only retry their own)
    if not current_user.is_admin and db_deployment.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this deployment")

    if db_deployment.status != 'DNS_FAILED':
        raise HTTPException(status_code=400, detail="Can only retry DNS validation for deployments with DNS_FAILED status")

    # Prevent concurrent operations
    if db_deployment.status in ['RESTARTING', 'DESTROYING', 'DNS_VALIDATION']:
        raise HTTPException(status_code=400, detail=f"Cannot retry DNS validation for deployment that is currently {db_deployment.status.lower()}")

    # Use deployment manager to retry DNS validation
    deployment_manager = DeploymentManager()

    # Schedule DNS validation retry in background
    background_tasks.add_task(deployment_manager.retry_dns_validation, db_deployment.id)

    return {"message": f"DNS validation retry initiated for deployment {deployment_id}"}


@router.get("/dns-ssl-status")
async def get_all_deployments_dns_ssl_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get DNS and SSL status for all user's deployments (or all if admin)"""
    # Get user's deployments or all if admin
    if current_user.is_admin:
        deployments = db.query(DeploymentModel).filter(
            DeploymentModel.status == 'ACTIVE',
            DeploymentModel.deleted_at.is_(None)
        ).all()
    else:
        deployments = db.query(DeploymentModel).filter(
            DeploymentModel.user_id == current_user.id,
            DeploymentModel.status == 'ACTIVE',
            DeploymentModel.deleted_at.is_(None)
        ).all()

    results = []
    for deployment in deployments:
        try:
            result = dns_ssl_checker.check_deployment_dns_ssl(deployment)
            results.append(result)
        except Exception as e:
            results.append({
                'deployment_id': deployment.id,
                'status': 'error',
                'message': f"DNS/SSL check error: {str(e)}",
                'needs_restart': False
            })

    return results


@router.post("/package-request", response_model=dict)
async def submit_package_request(
    request_data: dict,
    current_user: User = Depends(get_current_user)
):
    """
    Submit a package request from a user.
    Sends an email to administrators with the user's request.
    """
    package_name = request_data.get("package_name", "")
    reason = request_data.get("reason", "")

    if not package_name or not reason:
        raise HTTPException(status_code=400, detail="Package name and reason are required")

    # Send email to administrators
    subject = f"New Package Request: {package_name}"
    text_content = f"""
New package request from user {current_user.username} ({current_user.email}):

Package requested: {package_name}
Reason: {reason}

User ID: {current_user.id}
Submitted at: {datetime.utcnow().isoformat()}
"""

    # Send to a configured admin email or use a default
    admin_email = "<EMAIL>"  # This should be configurable

    try:
        success = _send_email(admin_email, subject, text_content)
        if success:
            return {"message": "Package request submitted successfully"}
        else:
            return {"message": "Package request received but email notification failed"}
    except Exception as e:
        # Log the error but don't fail the request
        print(f"Failed to send package request email: {e}")
        return {"message": "Package request received but email notification failed"}
