const axios = require('axios');
require('dotenv').config();

const DIGITALOCEAN_TOKEN = process.env.DIGITALOCEAN_TOKEN;

async function testDigitalOceanAPI() {
    console.log('🔍 Testing DigitalOcean API...');
    console.log('Token configured:', !!DIGITALOCEAN_TOKEN);
    
    if (!DIGITALOCEAN_TOKEN) {
        console.error('❌ DIGITALOCEAN_TOKEN not configured');
        return;
    }

    try {
        // Test 1: List droplets
        console.log('\n📋 Testing: List droplets');
        const dropletsRes = await axios.get('https://api.digitalocean.com/v2/droplets', {
            headers: { Authorization: `Bearer ${DIGITALOCEAN_TOKEN}` }
        });
        
        console.log(`✅ Found ${dropletsRes.data.droplets.length} droplets`);
        
        if (dropletsRes.data.droplets.length === 0) {
            console.log('⚠️  No droplets found');
            return;
        }

        const droplet = dropletsRes.data.droplets[0];
        console.log(`📊 Testing with droplet: ${droplet.name} (ID: ${droplet.id})`);
        console.log(`📊 Droplet size: ${droplet.size.slug}`);
        console.log(`📊 Droplet status: ${droplet.status}`);

        // Test 2: Get droplet details
        console.log('\n📋 Testing: Get droplet details');
        const detailsRes = await axios.get(`https://api.digitalocean.com/v2/droplets/${droplet.id}`, {
            headers: { Authorization: `Bearer ${DIGITALOCEAN_TOKEN}` }
        });
        console.log(`✅ Droplet memory: ${detailsRes.data.droplet.memory} MB`);
        console.log(`✅ Droplet vcpus: ${detailsRes.data.droplet.vcpus}`);

        // Test 3: Get bandwidth metrics (this is likely where the error occurs)
        console.log('\n📊 Testing: Get bandwidth metrics');
        const now = Math.floor(Date.now() / 1000);
        const oneHourAgo = now - 3600;
        
        try {
            console.log(`📊 Requesting outbound metrics from ${oneHourAgo} to ${now}`);
            const outboundRes = await axios.get(
                `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=public&direction=outbound&start=${oneHourAgo}&end=${now}`,
                {
                    headers: { Authorization: `Bearer ${DIGITALOCEAN_TOKEN}` },
                }
            );
            
            console.log('✅ Outbound response structure:', Object.keys(outboundRes.data));
            if (outboundRes.data.data) {
                console.log('📊 Outbound data structure:', Object.keys(outboundRes.data.data));
                console.log('📊 Outbound datapoints available:', !!outboundRes.data.data.datapoints);
                if (outboundRes.data.data.datapoints) {
                    console.log('📊 Outbound datapoints count:', outboundRes.data.data.datapoints.length);
                    if (outboundRes.data.data.datapoints.length > 0) {
                        console.log('📊 Sample datapoint:', outboundRes.data.data.datapoints[0]);
                    }
                } else {
                    console.log('⚠️  No datapoints in outbound response');
                }
            } else {
                console.log('⚠️  No data in outbound response');
            }
            
        } catch (metricsError) {
            console.error('❌ Bandwidth metrics error:', metricsError.response?.status, metricsError.response?.statusText);
            console.error('❌ Error details:', metricsError.response?.data);
        }

        // Test 4: Try different time ranges
        console.log('\n🔄 Testing: Different time ranges');
        const oneDayAgo = now - (24 * 3600);
        
        try {
            console.log(`📊 Requesting 24h metrics from ${oneDayAgo} to ${now}`);
            const dayRes = await axios.get(
                `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=public&direction=outbound&start=${oneDayAgo}&end=${now}`,
                {
                    headers: { Authorization: `Bearer ${DIGITALOCEAN_TOKEN}` },
                }
            );
            
            console.log('✅ 24h metrics retrieved');
            if (dayRes.data.data && dayRes.data.data.datapoints) {
                console.log('📊 24h datapoints count:', dayRes.data.data.datapoints.length);
            }
            
        } catch (dayError) {
            console.error('❌ 24h metrics error:', dayError.response?.status);
        }

    } catch (error) {
        console.error('❌ API Test failed:', error.response?.status, error.response?.statusText);
        console.error('❌ Error details:', error.response?.data);
    }
}

testDigitalOceanAPI();
