services:
  db:
    image: postgres:15-alpine
    container_name: manidae-postgres
    restart: always
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: manidae-backend
    restart: always
    depends_on:
      db:
        condition: service_healthy
    ports:
      - "8000:8000"
    env_file:
      - ./backend/.env
      - ./.env # For database credentials
    volumes:
      - ./backend/app:/app/app # Mount only the app directory for hot-reloading
      - /home/<USER>/Projects/AWSKeys/your-service-account-key.json:/root/keys/your-service-account-key.json:ro # Mount keys directory for credentials
    command: >
      sh -c "echo 'Running DB table creation...' &&
             python -m app.db.create_tables &&
             echo 'DB table creation complete. Running DB initialization...' &&
             python -m app.db.init_db &&
             echo 'DB initialization complete. Starting server...' &&
             uvicorn app.main:app --host 0.0.0.0 --port 8000"

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: manidae-frontend
    restart: always
    ports:
      - "5173:80" # Access via http://localhost:5173
    depends_on:
      - backend

  billing_job:
    build:
      context: ./backend # Re-uses the backend image
      dockerfile: Dockerfile
    container_name: manidae-billing-job
    restart: always
    depends_on:
      backend:   # instead of just db
        condition: service_started
    env_file:
      - ./backend/.env
    command: >
      sh -c "while true; do echo 'Running billing job...' && python -m app.services.billing_job && echo 'Billing job finished. Sleeping for 10 mins.' && sleep 600; done"

  bandwidth_monitor:
    build:
      context: ./scripts
      dockerfile: Dockerfile
    container_name: manidae-bandwidth-monitor
    restart: always
    env_file:
      - ./scripts/.env
    volumes:
      # Mount GCP credentials if using GCP (create this directory if needed)
      - ./scripts/gcp-keys:/app/keys:ro
      # Mount persistent storage for bandwidth tracking data
      - bandwidth_data:/app/data
    environment:
      # Override data file location for Docker
      - BANDWIDTH_DATA_FILE=/app/data/bandwidth-usage.json
    command: >
      sh -c "while true; do echo 'Running bandwidth monitor at $(date)' && node monitor.js && echo 'Bandwidth monitoring completed. Sleeping for 1 hour...' && sleep 3600; done"
    healthcheck:
      test: ["CMD", "node", "-e", "console.log('Health check passed')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

volumes:
  postgres_data:
    driver: local
  bandwidth_data:
    driver: local