from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
from decimal import Decimal
from datetime import datetime, timedelta
import secrets
import string
from typing import List, Optional

from app.db.session import get_db
from app.models.user import User
from app.models.referral_credit import ReferralCredit
from app.api.auth import get_current_user
from app.core.config import get_settings

router = APIRouter()

class ReferralLinkResponse(BaseModel):
    referral_code: str
    referral_url: str

class ReferralStatsResponse(BaseModel):
    referral_code: str
    total_referrals: int
    total_credits_earned: float
    active_credits: float
    expired_credits: float

class ReferralCreditResponse(BaseModel):
    id: int
    amount: float
    credit_type: str
    expires_at: Optional[datetime]
    used_amount: float
    remaining_amount: float
    is_active: bool
    is_expired: bool
    is_usable: bool
    created_at: datetime

def generate_referral_code() -> str:
    """Generate a unique referral code."""
    # Generate a 8-character alphanumeric code
    alphabet = string.ascii_uppercase + string.digits
    # Exclude confusing characters
    alphabet = alphabet.replace('0', '').replace('O', '').replace('1', '').replace('I', '')
    return ''.join(secrets.choice(alphabet) for _ in range(8))

@router.get("/my-link", response_model=ReferralLinkResponse)
def get_my_referral_link(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get or create the current user's referral link.
    """
    # Check if user already has a referral code
    if not current_user.referral_code:
        # Generate a unique referral code
        while True:
            code = generate_referral_code()
            # Check if code already exists
            existing = db.query(User).filter(User.referral_code == code).first()
            if not existing:
                current_user.referral_code = code
                db.add(current_user)
                db.commit()
                db.refresh(current_user)
                break
    
    # Create referral URL using configurable base URL
    settings = get_settings()
    referral_url = f"{settings.REFERRAL_BASE_URL}/signup?ref={current_user.referral_code}"
    
    return ReferralLinkResponse(
        referral_code=current_user.referral_code,
        referral_url=referral_url
    )

@router.get("/stats", response_model=ReferralStatsResponse)
def get_referral_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get referral statistics for the current user.
    """
    if not current_user.referral_code:
        return ReferralStatsResponse(
            referral_code="",
            total_referrals=0,
            total_credits_earned=0.0,
            active_credits=0.0,
            expired_credits=0.0
        )
    
    # Count total referrals
    total_referrals = db.query(User).filter(User.referred_by_id == current_user.id).count()
    
    # Get referral credits earned by this user (as referrer)
    referrer_credits = db.query(ReferralCredit).filter(
        ReferralCredit.user_id == current_user.id,
        ReferralCredit.credit_type == "referrer"
    ).all()
    
    total_credits_earned = sum(float(credit.amount) for credit in referrer_credits)
    active_credits = sum(float(credit.remaining_amount) for credit in referrer_credits if credit.is_usable)
    expired_credits = sum(float(credit.remaining_amount) for credit in referrer_credits if credit.is_expired)
    
    return ReferralStatsResponse(
        referral_code=current_user.referral_code,
        total_referrals=total_referrals,
        total_credits_earned=total_credits_earned,
        active_credits=active_credits,
        expired_credits=expired_credits
    )

@router.get("/credits", response_model=List[ReferralCreditResponse])
def get_my_referral_credits(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all referral credits for the current user.
    """
    credits = db.query(ReferralCredit).filter(
        ReferralCredit.user_id == current_user.id
    ).order_by(ReferralCredit.created_at.desc()).all()
    
    return [
        ReferralCreditResponse(
            id=credit.id,
            amount=float(credit.amount),
            credit_type=credit.credit_type,
            expires_at=credit.expires_at,
            used_amount=float(credit.used_amount),
            remaining_amount=float(credit.remaining_amount),
            is_active=credit.is_active,
            is_expired=credit.is_expired,
            is_usable=credit.is_usable,
            created_at=credit.created_at
        )
        for credit in credits
    ]

@router.get("/validate-code/{referral_code}")
def validate_referral_code(
    referral_code: str,
    db: Session = Depends(get_db)
):
    """
    Validate if a referral code exists and is valid.
    This is a public endpoint for the signup form.
    """
    user = db.query(User).filter(User.referral_code == referral_code).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Invalid referral code")
    
    return {
        "valid": True,
        "referrer_username": user.username,
        "message": f"Valid referral code from {user.username}"
    }
