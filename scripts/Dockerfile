# Use Node.js 18 Alpine for smaller image size
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies for native modules
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy application files
COPY . .

# Create necessary directories
RUN mkdir -p /app/data /app/keys

# Create a non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S monitor -u 1001 -G nodejs

# Change ownership of the app directory
RUN chown -R monitor:nodejs /app

# Switch to non-root user
USER monitor

# Health check to ensure the container is working
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node -e "console.log('Health check passed')" || exit 1

# Default command (will be overridden in docker-compose)
CMD ["node", "monitor.js"]
