import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts';
import { apiClient } from '../api';
import { Button } from '@/components/ui/button';
import ReferralRequestModal from '../components/ReferralRequestModal';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface PricingData {
  minBalanceToDeploy: string;
  byovpsDailyFee: string;
  supportLevel2Daily: string;
  supportLevel3Daily: string;
}

const Pricing: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [pricingData, setPricingData] = useState<PricingData>({
    minBalanceToDeploy: '4.99',
    byovpsDailyFee: '0.12',
    supportLevel2Daily: '1.76',
    supportLevel3Daily: '6.70'
  });
  const [loading, setLoading] = useState(true);
  const [showReferralModal, setShowReferralModal] = useState(false);

  useEffect(() => {
    const fetchPricingData = async () => {
      try {
        // Get BYOVPS pricing for different support levels
        const [level1Result, level2Result, level3Result] = await Promise.all([
          apiClient.calculatePricing({
            cloud_provider: null,
            instance_type: null,
            region: null,
            package: 'Pangolin',
            support_level: 'Level 1',
            server_type: 'vps'
          }),
          apiClient.calculatePricing({
            cloud_provider: null,
            instance_type: null,
            region: null,
            package: 'Pangolin',
            support_level: 'Level 2',
            server_type: 'vps'
          }),
          apiClient.calculatePricing({
            cloud_provider: null,
            instance_type: null,
            region: null,
            package: 'Pangolin',
            support_level: 'Level 3',
            server_type: 'vps'
          })
        ]);

        // Calculate daily costs from hourly rates
        const byovpsDailyFee = level1Result.daily_cost.toFixed(2);
        const supportLevel2Daily = level2Result.daily_cost.toFixed(2);
        const supportLevel3Daily = level3Result.daily_cost.toFixed(2);

        // Keep minimum balance from env (this is a business config, not pricing data)
        const minBalanceToDeploy = import.meta.env.VITE_MIN_BALANCE_TO_DEPLOY || '4.99';

        setPricingData({
          minBalanceToDeploy,
          byovpsDailyFee,
          supportLevel2Daily,
          supportLevel3Daily
        });
      } catch (error) {
        console.error('Failed to fetch pricing data:', error);
        // Ensure we have reasonable fallback values
        const minBalanceToDeploy = import.meta.env.VITE_MIN_BALANCE_TO_DEPLOY || '4.99';
        setPricingData({
          minBalanceToDeploy,
          byovpsDailyFee: '0.12',
          supportLevel2Daily: '1.76',
          supportLevel3Daily: '6.70'
        });

      } finally {
        setLoading(false);
      }
    };

    fetchPricingData();
  }, []);

  const handleGetStarted = () => {
    if (user) {
      navigate('/create-deployment');
    } else {
      navigate('/signup');
    }
  };

  const cloudProviders = [
    { name: 'Hetzner', popular: false },
    { name: 'DigitalOcean', popular: false },
    { name: 'Linode', popular: false },
    { name: 'Vultr', popular: false },
    { name: 'AWS', popular: false },
    { name: 'Google Cloud', popular: false },
    { name: 'Azure', popular: false }
  ];

  if (loading) {
    return (
      <div className="min-h-screen py-8 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading pricing information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold tracking-tight mb-4">
            Simple Pricing
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Deploy servers from as little as ${pricingData.minBalanceToDeploy}, then only pay for what you use.
            Delete anytime to stop charges immediately.
          </p>
        </div>

        {/* Pricing Breakdown */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">${pricingData.minBalanceToDeploy}</CardTitle>
              <CardDescription>Minimum balance to deploy</CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-3">
              <p className="text-sm text-muted-foreground">
                Required minimum account balance to create deployments.
                Add funds via Stripe or use referral credits.
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowReferralModal(true)}
              >
                Request Free Credits
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">Daily VPS</CardTitle>
              <CardDescription>Choose your infrastructure</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-sm text-muted-foreground mb-3">
                Bring your own VPS for a small management fee, or select from our cloud providers.
              </p>
              <p className="text-xs text-muted-foreground">
                Costs vary by provider, region & instance size
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">Daily Support</CardTitle>
              <CardDescription>Choose your support level</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-sm text-muted-foreground mb-3">
                Free community support or paid email/phone support with backups.
              </p>
              <p className="text-xs text-muted-foreground">
                from ${pricingData.byovpsDailyFee}/day • ${pricingData.supportLevel2Daily}/day • ${pricingData.supportLevel3Daily}/day
              </p>
            </CardContent>
          </Card>
        </div>

        {/* VPS Options */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-center mb-8">VPS Options</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>Bring Your Own VPS</CardTitle>
                <CardDescription>Use your existing server</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-2">${pricingData.byovpsDailyFee}/day</div>
                <p className="text-sm text-muted-foreground mb-4">Small management fee</p>
                <ul className="text-sm space-y-1">
                  <li>• Use any VPS provider</li>
                  <li>• Keep your existing setup</li>
                  <li>• Minimal additional cost</li>
                  <li>• You handle VPS billing</li>
                  <li>• Cancel at any time</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-primary">
              <CardHeader>
                <CardTitle>We Provide VPS</CardTitle>
                <CardDescription>Select from cloud providers</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-2">Varies</div>
                <p className="text-sm text-muted-foreground mb-4">Based on your choices</p>
                <ul className="text-sm space-y-1 mb-4">
                  <li>• Choose cloud provider</li>
                  <li>• Select region</li>
                  <li>• Pick instance size</li>
                  <li>• We handle everything</li>
                </ul>
                <div className="flex flex-wrap gap-1">
                  {cloudProviders.map((provider) => (
                    <span
                      key={provider.name}
                      className={`text-xs px-2 py-1 rounded ${
                        provider.popular
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted text-muted-foreground'
                      }`}
                    >
                      {provider.name}
                    </span>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Support Levels */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-center mb-8">Support Levels</h2>
          
          <div className="grid lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Community</CardTitle>
                <CardDescription>Self-service support</CardDescription>
              </CardHeader>
              <CardContent>
                Always<div className="text-2xl font-bold mb-4">FREE</div>
                <ul className="text-sm space-y-2">
                  <li>• Community forum</li>
                  <li>• Documentation</li>
                  <li>• Basic monitoring</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-primary">
              <CardHeader>
                <CardTitle>Professional</CardTitle>
                <CardDescription>Email support + backups</CardDescription>
              </CardHeader>
              <CardContent>
                from<div className="text-2xl font-bold mb-4">${pricingData.supportLevel2Daily}<span className="text-lg font-normal">/day</span></div>
                <ul className="text-sm space-y-2">
                  <li>• Email support (24h response)</li>
                  <li>• 14-day backups</li>
                  <li>• 2 snapshots included</li>
                  <li>• Priority queue</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Enterprise</CardTitle>
                <CardDescription>Phone support + extras</CardDescription>
              </CardHeader>
              <CardContent>
                from<div className="text-2xl font-bold mb-4">${pricingData.supportLevel3Daily}<span className="text-lg font-normal">/day</span></div>
                <ul className="text-sm space-y-2">
                  <li>• Email & phone support (4h response)</li>
                  <li>• 30-day backups</li>
                  <li>• 4 snapshots included</li>
                  <li>• Dedicated success manager</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* How It Works */}
        <div className="bg-muted/50 rounded-lg p-8 mb-12">
          <h3 className="text-lg font-semibold mb-6 text-center">How It Works</h3>
          <div className="grid md:grid-cols-4 gap-6 text-sm">
            <div className="text-center">
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-2 text-sm font-bold">1</div>
              <p className="font-medium mb-1">Sign Up</p>
              <p className="text-muted-foreground">Add ${pricingData.minBalanceToDeploy} minimum balance (via Stripe or referral credits)</p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-2 text-sm font-bold">2</div>
              <p className="font-medium mb-1">Choose VPS</p>
              <p className="text-muted-foreground">Bring your own or select provider/region/size</p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-2 text-sm font-bold">3</div>
              <p className="font-medium mb-1">Pick Support</p>
              <p className="text-muted-foreground">Community (free) or paid support levels</p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-2 text-sm font-bold">4</div>
              <p className="font-medium mb-1">Pay Daily</p>
              <p className="text-muted-foreground">Charged daily only while running</p>
            </div>
          </div>
        </div>

        {/* FAQ */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-center mb-8">FAQ</h2>
          <div className="space-y-6 max-w-2xl mx-auto">
            <div>
              <h3 className="font-semibold mb-2">How do I stop paying?</h3>
              <p className="text-muted-foreground text-sm">
                Delete your deployment anytime to immediately stop all charges. There are no pause options - 
                you either run and pay daily, or delete and pay nothing.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">What if I run out of credits?</h3>
              <p className="text-muted-foreground text-sm">
                Your deployment will be deleted automatically. Add credits and create a new deployment when ready. 
                You need at least ${pricingData.minBalanceToDeploy} balance to create deployments.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Can I change support levels?</h3>
              <p className="text-muted-foreground text-sm">
                Yes, upgrade or downgrade anytime. Changes take effect immediately and adjust your daily billing.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">What cloud providers do you support?</h3>
              <p className="text-muted-foreground text-sm">
                Hetzner, DigitalOcean, Linode, Vultr, AWS, Google Cloud, and Azure. 
                Prices vary by provider and region.
              </p>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-muted-foreground mb-6">
            Simple pricing, no surprises. Pay only for what you use.
          </p>
          <Button size="lg" onClick={handleGetStarted}>
            {user ? 'Create Deployment' : 'Sign Up Free'}
          </Button>
        </div>
      </div>

      {/* Referral Request Modal */}
      <ReferralRequestModal
        open={showReferralModal}
        onClose={() => setShowReferralModal(false)}
      />
    </div>
  );
};

export default Pricing;