do_token = "{{ do_token }}"
region = "{{ region }}"
do_image_slug = "{{ do_image_slug }}" # e.g., "ubuntu-22-04-x64"
ssh_public_key = "{{ ssh_public_key }}"
instance_type = "{{ instance_type }}" # e.g., "s-1vcpu-1gb"
instance_name = "{{ instance_name }}"
client_name = "{{ client_name }}"
client_name_lower = "{{ client_name_lower }}"
client_id = "{{ client_id }}"
domain = "{{ domain }}"
admin_email = "{{ admin_email }}"
admin_username = "{{ admin_username }}"
admin_password = "{{ admin_password }}"
admin_subdomain = "{{ admin_subdomain }}"
github_repo = "{{ github_repo }}"
komodo_provider_endpoint = "{{ komodo_provider_endpoint }}"
komodo_api_key = "{{ komodo_api_key }}"
komodo_api_secret = "{{ komodo_api_secret }}"
github_token = "{{ github_token }}"
{% if package in ["Pangolin+", "Pangolin+AI"] %}
# Pangolin+ Package Configuration
crowdsec_enrollment_key = "{{ crowdsec_enrollment_key or '' }}"
static_page_subdomain   = "{{ static_page_subdomain or '' }}"
maxmind_license_key     = "{{ maxmind_license_key or '' }}"
{% endif %}

{% if package == "Pangolin+AI" %}
# Pangolin+AI Additional Configuration
oauth_client_id         = "{{ oauth_client_id or '' }}"
oauth_client_secret     = "{{ oauth_client_secret or '' }}"
komodo_host_ip          = "{{ komodo_host_ip or '' }}"
komodo_passkey          = "{{ komodo_passkey or '' }}"
openai_api_key          = "{{ openai_api_key or '' }}"
{% endif %}