const axios = require('axios');
require('dotenv').config();

async function investigateTrafficIssue() {
    console.log('🔍 Investigating Traffic Detection Issues');
    console.log('========================================');
    console.log('');
    console.log('This script will help identify why some providers are not detecting');
    console.log('the 1GB downloads you performed on each VPS instance.');
    console.log('');

    // Test 1: Vultr Deep Dive
    if (process.env.VULTR_API_KEY) {
        console.log('🔧 VULTR DEEP INVESTIGATION');
        console.log('===========================');
        try {
            const instancesRes = await axios.get('https://api.vultr.com/v2/instances', {
                headers: { Authorization: `Bearer ${process.env.VULTR_API_KEY}` }
            });
            
            if (instancesRes.data.instances.length > 0) {
                const instance = instancesRes.data.instances[0];
                console.log(`📊 Instance: ${instance.label} (ID: ${instance.id})`);
                console.log(`📊 Status: ${instance.status}`);
                console.log(`📊 Plan: ${instance.plan}`);
                
                // Test bandwidth endpoint
                try {
                    const bandwidthRes = await axios.get(`https://api.vultr.com/v2/instances/${instance.id}/bandwidth`, {
                        headers: { Authorization: `Bearer ${process.env.VULTR_API_KEY}` }
                    });
                    
                    console.log(`✅ Bandwidth endpoint accessible`);
                    console.log(`📊 Response structure:`, JSON.stringify(bandwidthRes.data, null, 2));
                    
                    if (bandwidthRes.data.bandwidth) {
                        const bandwidth = bandwidthRes.data.bandwidth;
                        console.log(`📊 Bandwidth object keys:`, Object.keys(bandwidth));
                        
                        // Check for different date formats
                        const today = new Date().toISOString().split('T')[0];
                        const yesterday = new Date(Date.now() - 24*60*60*1000).toISOString().split('T')[0];
                        
                        console.log(`📊 Checking dates: ${today}, ${yesterday}`);
                        console.log(`📊 Today's data:`, bandwidth[today]);
                        console.log(`📊 Yesterday's data:`, bandwidth[yesterday]);
                        
                        // Look for any non-zero values
                        for (const [date, data] of Object.entries(bandwidth)) {
                            if (data && (data.incoming_bytes > 0 || data.outgoing_bytes > 0)) {
                                console.log(`🎉 Found traffic on ${date}:`, data);
                            }
                        }
                    }
                } catch (bandwidthError) {
                    console.log(`❌ Bandwidth error: ${bandwidthError.response?.data || bandwidthError.message}`);
                }
            }
        } catch (error) {
            console.log(`❌ Vultr error: ${error.message}`);
        }
        console.log('');
    }

    // Test 2: DigitalOcean Deep Dive
    if (process.env.DIGITALOCEAN_TOKEN) {
        console.log('🔧 DIGITALOCEAN DEEP INVESTIGATION');
        console.log('==================================');
        try {
            const dropletsRes = await axios.get('https://api.digitalocean.com/v2/droplets', {
                headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` }
            });
            
            if (dropletsRes.data.droplets.length > 0) {
                const droplet = dropletsRes.data.droplets[0];
                console.log(`📊 Droplet: ${droplet.name} (ID: ${droplet.id})`);
                console.log(`📊 Status: ${droplet.status}`);
                console.log(`📊 Size: ${droplet.size.slug}`);
                
                // Test different time ranges
                const timeRanges = [
                    { name: 'Last 1 hour', hours: 1 },
                    { name: 'Last 6 hours', hours: 6 },
                    { name: 'Last 24 hours', hours: 24 },
                    { name: 'Last 48 hours', hours: 48 }
                ];
                
                for (const range of timeRanges) {
                    try {
                        const now = Math.floor(Date.now() / 1000);
                        const start = now - (range.hours * 3600);
                        
                        const bandwidthRes = await axios.get(
                            `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=public&direction=outbound&start=${start}&end=${now}`,
                            { headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` } }
                        );
                        
                        if (bandwidthRes.data.data && bandwidthRes.data.data.result) {
                            const results = bandwidthRes.data.data.result;
                            let totalBytes = 0;
                            
                            for (const result of results) {
                                if (result.values && Array.isArray(result.values)) {
                                    for (const [timestamp, value] of result.values) {
                                        totalBytes += parseFloat(value) * 300; // 5-minute intervals
                                    }
                                }
                            }
                            
                            console.log(`📊 ${range.name}: ${totalBytes} bytes (${(totalBytes / (1024**3)).toFixed(6)} GB)`);
                            if (totalBytes > 1024*1024) { // More than 1MB
                                console.log(`🎉 Found significant traffic in ${range.name}!`);
                            }
                        }
                    } catch (rangeError) {
                        console.log(`❌ ${range.name} error: ${rangeError.message}`);
                    }
                }
            }
        } catch (error) {
            console.log(`❌ DigitalOcean error: ${error.message}`);
        }
        console.log('');
    }

    // Test 3: AWS Lightsail Deep Dive
    if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
        console.log('🔧 AWS LIGHTSAIL DEEP INVESTIGATION');
        console.log('===================================');
        try {
            const { LightsailClient, GetInstancesCommand } = require("@aws-sdk/client-lightsail");
            const { CloudWatchClient, GetMetricStatisticsCommand } = require("@aws-sdk/client-cloudwatch");
            
            const lightsailClient = new LightsailClient({
                region: process.env.AWS_REGION || 'us-east-1',
                credentials: {
                    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
                }
            });
            
            const cloudwatchClient = new CloudWatchClient({
                region: process.env.AWS_REGION || 'us-east-1',
                credentials: {
                    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
                }
            });
            
            const instancesRes = await lightsailClient.send(new GetInstancesCommand({}));
            
            if (instancesRes.instances && instancesRes.instances.length > 0) {
                const instance = instancesRes.instances[0];
                console.log(`📊 Instance: ${instance.name}`);
                console.log(`📊 State: ${instance.state?.name}`);
                console.log(`📊 Bundle: ${instance.bundleId}`);
                
                // Test different time periods
                const timePeriods = [
                    { name: 'Last 1 hour', hours: 1 },
                    { name: 'Last 6 hours', hours: 6 },
                    { name: 'Last 24 hours', hours: 24 },
                    { name: 'Last 48 hours', hours: 48 }
                ];
                
                for (const period of timePeriods) {
                    try {
                        const now = new Date();
                        const start = new Date(now.getTime() - (period.hours * 60 * 60 * 1000));
                        
                        const statsRes = await cloudwatchClient.send(new GetMetricStatisticsCommand({
                            Namespace: 'AWS/Lightsail',
                            MetricName: 'NetworkOut',
                            Dimensions: [{ Name: 'InstanceName', Value: instance.name }],
                            StartTime: start,
                            EndTime: now,
                            Period: 3600,
                            Statistics: ['Sum']
                        }));
                        
                        if (statsRes.Datapoints && statsRes.Datapoints.length > 0) {
                            const totalBytes = statsRes.Datapoints.reduce((sum, dp) => sum + (dp.Sum || 0), 0);
                            console.log(`📊 ${period.name}: ${totalBytes} bytes (${(totalBytes / (1024**3)).toFixed(6)} GB)`);
                            if (totalBytes > 1024*1024) {
                                console.log(`🎉 Found significant traffic in ${period.name}!`);
                            }
                        } else {
                            console.log(`📊 ${period.name}: No datapoints`);
                        }
                    } catch (periodError) {
                        console.log(`❌ ${period.name} error: ${periodError.message}`);
                    }
                }
            }
        } catch (error) {
            console.log(`❌ Lightsail error: ${error.message}`);
        }
        console.log('');
    }

    console.log('🎯 ANALYSIS SUMMARY');
    console.log('==================');
    console.log('');
    console.log('Based on the investigation:');
    console.log('');
    console.log('✅ Working providers:');
    console.log('  - Linode: 1.000 GB (account-level)');
    console.log('  - Azure: 0.009 GB (9 MB)');
    console.log('  - Hetzner: 0.001 GB (1 MB)');
    console.log('');
    console.log('❓ Possible reasons for missing data:');
    console.log('  1. Downloads may not have completed successfully');
    console.log('  2. Some providers only count certain types of traffic');
    console.log('  3. Metrics collection delays vary by provider');
    console.log('  4. Different measurement methodologies');
    console.log('');
    console.log('💡 Recommendations:');
    console.log('  1. SSH into each VPS and verify downloads completed');
    console.log('  2. Try generating traffic again with verification');
    console.log('  3. Check if VPS instances have proper internet connectivity');
    console.log('  4. Some providers may need longer delays (24-48 hours)');
}

investigateTrafficIssue().catch(console.error);
