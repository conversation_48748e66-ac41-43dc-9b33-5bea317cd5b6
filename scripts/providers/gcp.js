const {InstancesClient} = require("@google-cloud/compute");
const {MetricServiceClient} = require("@google-cloud/monitoring");

class GCPProvider {
    constructor(projectId, credentialsFile) {
        this.projectId = projectId;
        this.credentialsFile = credentialsFile;
        this.name = "GCP";

        this.instancesClient = this.credentialsFile ? new InstancesClient({
            projectId: this.projectId,
            keyFilename: this.credentialsFile,
        }) : null;

        this.monitoringClient = this.credentialsFile ? new MetricServiceClient({
            projectId: this.projectId,
            keyFilename: this.credentialsFile,
        }) : null;
    }

    async getBandwidthUsage(instanceName, zone, numericInstanceId) {
        if (!this.monitoringClient) {
            console.log("GCP: No monitoring client configured, using placeholder");
            return { outgoingBytes: 0, includedBytes: 0 };
        }

        try {
            // Get current time and 24 hours ago
            const now = new Date();
            const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

            // Query for network sent bytes (outgoing traffic)
            const request = {
                name: `projects/${this.projectId}`,
                filter: `metric.type="compute.googleapis.com/instance/network/sent_bytes_count" AND resource.labels.instance_id="${numericInstanceId}"`,
                interval: {
                    endTime: {
                        seconds: Math.floor(now.getTime() / 1000),
                    },
                    startTime: {
                        seconds: Math.floor(oneDayAgo.getTime() / 1000),
                    },
                },
                view: 'FULL',
            };

            const [timeSeries] = await this.monitoringClient.listTimeSeries(request);

            let totalOutgoingBytes = 0;

            if (timeSeries && timeSeries.length > 0) {
                // Sum up all the data points for the last 24 hours
                for (const series of timeSeries) {
                    if (series.points && series.points.length > 0) {
                        for (const point of series.points) {
                            if (point.value && point.value.int64Value) {
                                totalOutgoingBytes += parseInt(point.value.int64Value);
                            }
                        }
                    }
                }
            }

            // GCP doesn't have a fixed bandwidth limit like some other providers
            // Most GCP instances have generous network egress allowances
            // For monitoring purposes, we'll use a reasonable default limit
            const includedBytes = 1024 * 1024 * 1024 * 1024; // 1TB default limit

            console.log(`GCP: Instance ${instanceName} used ${(totalOutgoingBytes / (1024**3)).toFixed(2)} GB in the last 24 hours`);

            return {
                outgoingBytes: totalOutgoingBytes,
                includedBytes: includedBytes,
            };

        } catch (error) {
            console.error(`GCP: Failed to get bandwidth for instance ${instanceName}:`, error.message);
            // Return placeholder values on error
            return { outgoingBytes: 0, includedBytes: 1024 * 1024 * 1024 * 1024 }; // 1TB default
        }
    }

    async stopInstance(instanceId, zone) {
        if (!this.instancesClient) {
            console.log("GCP: No credentials configured, skipping");
            return false;
        }
        try {
            // instanceId here is the instance name, not the full selfLink
            await this.instancesClient.stop({
                project: this.projectId,
                zone: zone,
                instance: instanceId,
            });
            console.log(`GCP: Instance ${instanceId} in zone ${zone} stopped.`);
            return true;
        } catch (error) {
            console.error(`GCP: Failed to stop instance ${instanceId} in zone ${zone}:`, error.message);
            return false;
        }
    }

    async listServers() {
        if (!this.instancesClient) {
            console.log("GCP: No credentials configured, skipping");
            return [];
        }
        try {
            const iterable = this.instancesClient.aggregatedListAsync({
                project: this.projectId,
            });

            const instances = [];
            for await (const [zone, instancesObject] of iterable) {
                if (instancesObject && instancesObject.instances) {
                    for (const instance of instancesObject.instances) {
                        instances.push({
                            id: instance.name, // Use name as ID for stopping instances
                            name: instance.name,
                            numericId: instance.id, // Store the numeric ID for monitoring
                            zone: zone.replace("zones/", ""), // Extract zone name
                            provider: this.name,
                        });
                    }
                }
            }
            return instances;
        } catch (error) {
            console.error("GCP: Failed to list instances:", error.message);
            return [];
        }
    }
}

module.exports = GCPProvider;
