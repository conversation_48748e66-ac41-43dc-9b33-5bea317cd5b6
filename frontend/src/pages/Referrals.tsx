import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts';
import { apiClient } from '../api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Copy, Share2, Users, Gift } from 'lucide-react';

const Referrals: React.FC = () => {
  const { user } = useAuth();
  const [referralLink, setReferralLink] = useState<string>('');
  const [referralCode, setReferralCode] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);

  useEffect(() => {
    const loadReferralData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load referral link
        const linkData = await apiClient.getMyReferralLink();
        setReferralLink(linkData.referral_url);
        setReferralCode(linkData.referral_code);

      } catch (e: any) {
        setError(e?.message || 'Failed to load referral data');
      } finally {
        setLoading(false);
      }
    };

    loadReferralData();
  }, []);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const shareReferralLink = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Join Manidae Cloud',
          text: `Get started with cloud deployments and receive €${import.meta.env.VITE_REFERRAL_CREDIT_AMOUNT || '5'} credit!`,
          url: referralLink,
        });
      } catch (err) {
        console.error('Error sharing:', err);
      }
    } else {
      copyToClipboard(referralLink);
    }
  };



  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading referral data...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Referral Program</h1>
          <p className="text-muted-foreground">
            Invite friends and earn credits together
          </p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Referral Link Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="w-5 h-5" />
            Your Referral Link
          </CardTitle>
          <CardDescription>
            Share this link with friends to earn €{import.meta.env.VITE_REFERRER_CREDIT_AMOUNT || '5'} for each successful signup
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={referralLink}
              readOnly
              className="flex-1"
            />
            <Button
              onClick={() => copyToClipboard(referralLink)}
              variant="outline"
              size="icon"
            >
              <Copy className="w-4 h-4" />
            </Button>
            <Button onClick={shareReferralLink}>
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
          </div>
          
          {copySuccess && (
            <div className="text-sm text-green-600">
              ✅ Copied to clipboard!
            </div>
          )}

          <div className="text-sm text-muted-foreground">
            <strong>Your referral code:</strong> {referralCode}
          </div>
        </CardContent>
      </Card>



      {/* How it works */}
      <Card>
        <CardHeader>
          <CardTitle>How it works</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <Share2 className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-medium mb-1">1. Share your link</h3>
              <p className="text-sm text-muted-foreground">
                Send your referral link to friends and colleagues
              </p>
            </div>
            
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-medium mb-1">2. They sign up</h3>
              <p className="text-sm text-muted-foreground">
                Your friend creates an account using your referral code
              </p>
            </div>
            
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <Gift className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-medium mb-1">3. Both get credits</h3>
              <p className="text-sm text-muted-foreground">
                You receive €{import.meta.env.VITE_REFERRER_CREDIT_AMOUNT || '5'} and they receive €{import.meta.env.VITE_REFERRAL_CREDIT_AMOUNT || '5'} in account credits (expires in 30 days)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Referrals;
