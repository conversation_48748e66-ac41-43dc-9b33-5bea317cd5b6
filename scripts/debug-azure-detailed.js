const { DefaultAzureCredential } = require("@azure/identity");
const { ComputeManagementClient } = require("@azure/arm-compute");
const { MetricsQueryClient } = require("@azure/monitor-query");
require('dotenv').config();

async function debugAzureDetailed() {
    console.log('🔍 Detailed Azure Bandwidth Debugging');
    console.log('=====================================');
    console.log('');
    console.log('Investigating why Azure shows only 9MB when you downloaded 1GB...');
    console.log('');

    const subscriptionId = process.env.AZURE_SUBSCRIPTION_ID;
    
    if (!subscriptionId) {
        console.error('❌ AZURE_SUBSCRIPTION_ID not configured');
        return;
    }

    try {
        const credential = new DefaultAzureCredential();
        const computeClient = new ComputeManagementClient(credential, subscriptionId);
        const metricsClient = new MetricsQueryClient(credential);

        // Step 1: Find the VM using the existing approach
        console.log('📋 Step 1: Finding Azure VM');
        const resourceGroups = await computeClient.resourceGroups.list();
        let vm = null;
        let vmResourceGroup = null;

        for await (const rg of resourceGroups) {
            try {
                const vms = await computeClient.virtualMachines.list(rg.name);
                for await (const vmItem of vms) {
                    if (vmItem.name.includes('pangolin-test')) {
                        vm = vmItem;
                        vmResourceGroup = rg.name;
                        break;
                    }
                }
                if (vm) break;
            } catch (error) {
                console.log(`Skipping resource group ${rg.name}: ${error.message}`);
            }
        }

        if (!vm) {
            console.log('❌ VM not found');
            return;
        }

        console.log(`✅ Found VM: ${vm.name}`);
        console.log(`📊 Resource Group: ${vmResourceGroup}`);
        console.log(`📊 VM Size: ${vm.hardwareProfile?.vmSize}`);
        console.log(`📊 VM ID: ${vm.id}`);
        console.log('');

        // Step 2: Test different time ranges and metrics
        console.log('📋 Step 2: Testing different time ranges and metrics');
        
        const now = new Date();
        const timeRanges = [
            { name: 'Last 1 hour', start: new Date(now.getTime() - (1 * 60 * 60 * 1000)) },
            { name: 'Last 6 hours', start: new Date(now.getTime() - (6 * 60 * 60 * 1000)) },
            { name: 'Last 24 hours', start: new Date(now.getTime() - (24 * 60 * 60 * 1000)) },
            { name: 'Last 48 hours', start: new Date(now.getTime() - (48 * 60 * 60 * 1000)) }
        ];

        const metrics = [
            { name: 'Network Out Total', description: 'Total outbound network traffic' },
            { name: 'Network In Total', description: 'Total inbound network traffic' }
        ];

        for (const metric of metrics) {
            console.log(`\n🔍 Testing metric: ${metric.name} (${metric.description})`);
            
            for (const range of timeRanges) {
                try {
                    console.log(`\n  📊 ${range.name} (${range.start.toISOString()} to ${now.toISOString()})`);
                    
                    const metricsResponse = await metricsClient.queryResource(
                        vm.id,
                        [metric.name],
                        {
                            granularity: "PT1H", // 1 hour granularity
                            timespan: `${range.start.toISOString()}/${now.toISOString()}`
                        }
                    );

                    if (metricsResponse.metrics && metricsResponse.metrics.length > 0) {
                        const metricData = metricsResponse.metrics[0];
                        console.log(`    ✅ Metric available: ${metricData.name}`);
                        console.log(`    📊 Unit: ${metricData.unit}`);
                        
                        if (metricData.timeseries && metricData.timeseries.length > 0) {
                            const timeseries = metricData.timeseries[0];
                            console.log(`    📊 Data points: ${timeseries.data?.length || 0}`);
                            
                            if (timeseries.data && timeseries.data.length > 0) {
                                // Calculate total from all data points
                                let total = 0;
                                let maxValue = 0;
                                let nonZeroPoints = 0;
                                
                                for (const dataPoint of timeseries.data) {
                                    const value = dataPoint.total || dataPoint.average || dataPoint.maximum || 0;
                                    total += value;
                                    if (value > maxValue) maxValue = value;
                                    if (value > 0) nonZeroPoints++;
                                }
                                
                                console.log(`    📊 Total: ${total} bytes (${(total / (1024**3)).toFixed(6)} GB)`);
                                console.log(`    📊 Max single point: ${maxValue} bytes (${(maxValue / (1024**3)).toFixed(6)} GB)`);
                                console.log(`    📊 Non-zero points: ${nonZeroPoints}/${timeseries.data.length}`);
                                
                                if (total > 100 * 1024 * 1024) { // More than 100MB
                                    console.log(`    🎉 FOUND SIGNIFICANT TRAFFIC! ${(total / (1024**3)).toFixed(3)} GB`);
                                }
                                
                                // Show sample data points
                                if (timeseries.data.length > 0) {
                                    console.log(`    📊 Sample data points:`);
                                    const sampleCount = Math.min(3, timeseries.data.length);
                                    for (let i = 0; i < sampleCount; i++) {
                                        const dp = timeseries.data[i];
                                        const value = dp.total || dp.average || dp.maximum || 0;
                                        console.log(`      ${dp.timeStamp}: ${value} bytes (${(value / (1024**2)).toFixed(2)} MB)`);
                                    }
                                }
                            } else {
                                console.log(`    ⚠️  No data points available`);
                            }
                        } else {
                            console.log(`    ⚠️  No timeseries data available`);
                        }
                    } else {
                        console.log(`    ❌ No metrics data returned`);
                    }
                } catch (error) {
                    console.log(`    ❌ Error: ${error.message}`);
                }
            }
        }

        // Step 3: Check current provider implementation
        console.log('\n📋 Step 3: Current provider implementation analysis');
        console.log('Our current Azure provider is using:');
        console.log('- Metric: "Network Out Total"');
        console.log('- Granularity: PT1H (1 hour)');
        console.log('- Time range: Default (last hour)');
        console.log('- Aggregation: dataPoint.total');
        console.log('');
        console.log('💡 Possible issues:');
        console.log('1. Time range too narrow (only last hour)');
        console.log('2. Wrong metric (should check Network In Total for downloads)');
        console.log('3. Wrong aggregation method');
        console.log('4. Azure metrics delay');

    } catch (error) {
        console.error('❌ Debug failed:', error.message);
        console.error('Full error:', error);
    }
}

debugAzureDetailed();
