// Test Azure Monitor imports
console.log('Testing Azure Monitor package imports...');

try {
    const monitorQuery = require("@azure/monitor-query");
    console.log('✅ @azure/monitor-query exports:', Object.keys(monitorQuery));
} catch (error) {
    console.log('❌ @azure/monitor-query error:', error.message);
}

try {
    const monitorQueryMetrics = require("@azure/monitor-query-metrics");
    console.log('✅ @azure/monitor-query-metrics exports:', Object.keys(monitorQueryMetrics));
} catch (error) {
    console.log('❌ @azure/monitor-query-metrics error:', error.message);
}

try {
    const armMonitor = require("@azure/arm-monitor");
    console.log('✅ @azure/arm-monitor exports:', Object.keys(armMonitor));
} catch (error) {
    console.log('❌ @azure/arm-monitor error:', error.message);
}
