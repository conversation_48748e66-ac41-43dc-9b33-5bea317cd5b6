const axios = require('axios');
require('dotenv').config();

async function debugAllProviders() {
    console.log('🔍 Comprehensive Provider Debugging');
    console.log('===================================');
    console.log('');

    // Test Hetzner
    if (process.env.HCLOUD_TOKEN) {
        console.log('🔧 HETZNER DEBUGGING');
        console.log('====================');
        try {
            const serversRes = await axios.get('https://api.hetzner.cloud/v1/servers', {
                headers: { Authorization: `Bearer ${process.env.HCLOUD_TOKEN}` }
            });
            
            if (serversRes.data.servers.length > 0) {
                const server = serversRes.data.servers[0];
                console.log(`📊 Server: ${server.name} (ID: ${server.id})`);
                
                // Try metrics endpoint
                try {
                    const metricsRes = await axios.get(`https://api.hetzner.cloud/v1/servers/${server.id}/metrics?type=network&start=${new Date(Date.now() - 24*60*60*1000).toISOString()}&end=${new Date().toISOString()}`, {
                        headers: { Authorization: `Bearer ${process.env.HCLOUD_TOKEN}` }
                    });
                    
                    console.log(`✅ Metrics available: ${metricsRes.data.metrics ? 'Yes' : 'No'}`);
                    if (metricsRes.data.metrics && metricsRes.data.metrics.network) {
                        const network = metricsRes.data.metrics.network;
                        console.log(`📊 Network out points: ${network.out ? network.out.values.length : 0}`);
                        if (network.out && network.out.values.length > 0) {
                            const totalOut = network.out.values.reduce((sum, point) => sum + parseFloat(point[1]), 0);
                            console.log(`📊 Total outbound: ${totalOut} bytes (${(totalOut / (1024**3)).toFixed(6)} GB)`);
                        }
                    }
                } catch (metricsError) {
                    console.log(`❌ Metrics error: ${metricsError.message}`);
                }
            }
        } catch (error) {
            console.log(`❌ Hetzner error: ${error.message}`);
        }
        console.log('');
    }

    // Test Vultr
    if (process.env.VULTR_API_KEY) {
        console.log('🔧 VULTR DEBUGGING');
        console.log('==================');
        try {
            const instancesRes = await axios.get('https://api.vultr.com/v2/instances', {
                headers: { Authorization: `Bearer ${process.env.VULTR_API_KEY}` }
            });
            
            if (instancesRes.data.instances.length > 0) {
                const instance = instancesRes.data.instances[0];
                console.log(`📊 Instance: ${instance.label} (ID: ${instance.id})`);
                
                // Try bandwidth endpoint
                try {
                    const bandwidthRes = await axios.get(`https://api.vultr.com/v2/instances/${instance.id}/bandwidth`, {
                        headers: { Authorization: `Bearer ${process.env.VULTR_API_KEY}` }
                    });
                    
                    console.log(`✅ Bandwidth data available`);
                    console.log(`📊 Bandwidth structure:`, Object.keys(bandwidthRes.data));
                    if (bandwidthRes.data.bandwidth) {
                        console.log(`📊 Bandwidth keys:`, Object.keys(bandwidthRes.data.bandwidth));
                    }
                } catch (bandwidthError) {
                    console.log(`❌ Bandwidth error: ${bandwidthError.message}`);
                }
            }
        } catch (error) {
            console.log(`❌ Vultr error: ${error.message}`);
        }
        console.log('');
    }

    // Test DigitalOcean
    if (process.env.DIGITALOCEAN_TOKEN) {
        console.log('🔧 DIGITALOCEAN DEBUGGING');
        console.log('=========================');
        try {
            const dropletsRes = await axios.get('https://api.digitalocean.com/v2/droplets', {
                headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` }
            });
            
            if (dropletsRes.data.droplets.length > 0) {
                const droplet = dropletsRes.data.droplets[0];
                console.log(`📊 Droplet: ${droplet.name} (ID: ${droplet.id})`);
                
                // Try different time ranges for bandwidth
                const timeRanges = [
                    { name: '1 hour', start: Math.floor(Date.now() / 1000) - 3600 },
                    { name: '6 hours', start: Math.floor(Date.now() / 1000) - (6 * 3600) },
                    { name: '24 hours', start: Math.floor(Date.now() / 1000) - (24 * 3600) }
                ];
                
                for (const range of timeRanges) {
                    try {
                        const now = Math.floor(Date.now() / 1000);
                        const bandwidthRes = await axios.get(
                            `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=public&direction=outbound&start=${range.start}&end=${now}`,
                            { headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` } }
                        );
                        
                        console.log(`✅ ${range.name} bandwidth data available`);
                        if (bandwidthRes.data.data && bandwidthRes.data.data.result) {
                            console.log(`📊 Result count: ${bandwidthRes.data.data.result.length}`);
                            if (bandwidthRes.data.data.result.length > 0) {
                                const result = bandwidthRes.data.data.result[0];
                                if (result.values) {
                                    console.log(`📊 Values count: ${result.values.length}`);
                                    if (result.values.length > 0) {
                                        const totalBytes = result.values.reduce((sum, val) => sum + parseFloat(val[1]) * 300, 0);
                                        console.log(`📊 Total bytes (${range.name}): ${totalBytes} (${(totalBytes / (1024**3)).toFixed(6)} GB)`);
                                    }
                                }
                            }
                        }
                        break; // If one works, don't try others
                    } catch (bandwidthError) {
                        console.log(`❌ ${range.name} bandwidth error: ${bandwidthError.message}`);
                    }
                }
            }
        } catch (error) {
            console.log(`❌ DigitalOcean error: ${error.message}`);
        }
        console.log('');
    }

    console.log('💡 Summary:');
    console.log('- Linode: ✅ Fixed (using account-level data)');
    console.log('- Azure: ✅ Working (showing real usage)');
    console.log('- Others: Need investigation based on above results');
}

debugAllProviders().catch(console.error);
