const axios = require('axios');
require('dotenv').config();

async function testProviderAPIs() {
    console.log('🔍 Testing Provider API Responses');
    console.log('=================================');
    console.log('');
    console.log('This script tests the raw API responses to understand');
    console.log('why some providers are not showing the expected 1GB usage.');
    console.log('');

    // Test Hetzner - Check if we're missing something
    if (process.env.HCLOUD_TOKEN) {
        console.log('🔧 HETZNER API DEEP DIVE');
        console.log('========================');
        try {
            const serversRes = await axios.get('https://api.hetzner.cloud/v1/servers', {
                headers: { Authorization: `Bearer ${process.env.HCLOUD_TOKEN}` }
            });
            
            if (serversRes.data.servers.length > 0) {
                const server = serversRes.data.servers[0];
                console.log(`📊 Server: ${server.name}`);
                console.log(`📊 Full server object keys:`, Object.keys(server));
                console.log(`📊 Outgoing traffic: ${server.outgoing_traffic}`);
                console.log(`📊 Included traffic: ${server.included_traffic}`);
                console.log(`📊 Ingoing traffic: ${server.ingoing_traffic || 'Not available'}`);
                
                // Check if there are other traffic-related fields
                const trafficFields = Object.keys(server).filter(key => 
                    key.toLowerCase().includes('traffic') || 
                    key.toLowerCase().includes('bandwidth') ||
                    key.toLowerCase().includes('network')
                );
                console.log(`📊 Traffic-related fields:`, trafficFields);
                
                // Get detailed server info
                try {
                    const detailRes = await axios.get(`https://api.hetzner.cloud/v1/servers/${server.id}`, {
                        headers: { Authorization: `Bearer ${process.env.HCLOUD_TOKEN}` }
                    });
                    console.log(`📊 Detailed server traffic info:`);
                    console.log(`   Outgoing: ${detailRes.data.server.outgoing_traffic} bytes`);
                    console.log(`   Included: ${detailRes.data.server.included_traffic} bytes`);
                    if (detailRes.data.server.ingoing_traffic !== undefined) {
                        console.log(`   Ingoing: ${detailRes.data.server.ingoing_traffic} bytes`);
                    }
                } catch (error) {
                    console.log(`❌ Detailed server error: ${error.message}`);
                }
            }
        } catch (error) {
            console.log(`❌ Hetzner error: ${error.message}`);
        }
        console.log('');
    }

    // Test DigitalOcean - Check if we're using the right endpoints
    if (process.env.DIGITALOCEAN_TOKEN) {
        console.log('🔧 DIGITALOCEAN API DEEP DIVE');
        console.log('=============================');
        try {
            const dropletsRes = await axios.get('https://api.digitalocean.com/v2/droplets', {
                headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` }
            });
            
            if (dropletsRes.data.droplets.length > 0) {
                const droplet = dropletsRes.data.droplets[0];
                console.log(`📊 Droplet: ${droplet.name}`);
                
                // Test if there's a simpler bandwidth endpoint
                try {
                    const bandwidthRes = await axios.get(`https://api.digitalocean.com/v2/droplets/${droplet.id}/bandwidth`, {
                        headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` }
                    });
                    console.log(`📊 Simple bandwidth endpoint response:`, bandwidthRes.data);
                } catch (bandwidthError) {
                    console.log(`❌ Simple bandwidth endpoint not available: ${bandwidthError.response?.status}`);
                }
                
                // Check monitoring metrics with minimal parameters
                try {
                    const now = Math.floor(Date.now() / 1000);
                    const oneWeekAgo = now - (7 * 24 * 3600);
                    
                    console.log(`📊 Testing 7-day period: ${new Date(oneWeekAgo * 1000).toISOString()} to ${new Date(now * 1000).toISOString()}`);
                    
                    const metricsRes = await axios.get(
                        `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=public&direction=inbound&start=${oneWeekAgo}&end=${now}`,
                        { headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` } }
                    );
                    
                    console.log(`📊 7-day metrics response structure:`, Object.keys(metricsRes.data));
                    if (metricsRes.data.data) {
                        console.log(`📊 Data structure:`, Object.keys(metricsRes.data.data));
                        if (metricsRes.data.data.result) {
                            console.log(`📊 Results count: ${metricsRes.data.data.result.length}`);
                            if (metricsRes.data.data.result.length > 0) {
                                const result = metricsRes.data.data.result[0];
                                console.log(`📊 First result structure:`, Object.keys(result));
                                if (result.values) {
                                    console.log(`📊 Values count: ${result.values.length}`);
                                    if (result.values.length > 0) {
                                        console.log(`📊 Sample values:`, result.values.slice(0, 5));
                                        
                                        // Calculate total
                                        let total = 0;
                                        for (const [timestamp, value] of result.values) {
                                            total += parseFloat(value) * 300; // 5-minute intervals
                                        }
                                        console.log(`📊 Total calculated: ${total} bytes (${(total / (1024**3)).toFixed(6)} GB)`);
                                    }
                                }
                            }
                        }
                    }
                } catch (metricsError) {
                    console.log(`❌ 7-day metrics error: ${metricsError.message}`);
                }
            }
        } catch (error) {
            console.log(`❌ DigitalOcean error: ${error.message}`);
        }
        console.log('');
    }

    // Test AWS Lightsail - Check if CloudWatch is the issue
    if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
        console.log('🔧 AWS LIGHTSAIL API DEEP DIVE');
        console.log('==============================');
        try {
            const { LightsailClient, GetInstancesCommand, GetInstanceMetricDataCommand } = require("@aws-sdk/client-lightsail");
            
            const lightsailClient = new LightsailClient({
                region: process.env.AWS_REGION || 'us-east-1',
                credentials: {
                    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
                }
            });
            
            const instancesRes = await lightsailClient.send(new GetInstancesCommand({}));
            
            if (instancesRes.instances && instancesRes.instances.length > 0) {
                const instance = instancesRes.instances[0];
                console.log(`📊 Instance: ${instance.name}`);
                
                // Try Lightsail's own metric endpoint instead of CloudWatch
                try {
                    const now = new Date();
                    const oneWeekAgo = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
                    
                    const metricsRes = await lightsailClient.send(new GetInstanceMetricDataCommand({
                        instanceName: instance.name,
                        metricName: 'NetworkIn',
                        period: 3600, // 1 hour
                        startTime: oneWeekAgo,
                        endTime: now,
                        statistics: ['Sum']
                    }));
                    
                    console.log(`📊 Lightsail native metrics response:`, metricsRes);
                    if (metricsRes.metricData && metricsRes.metricData.length > 0) {
                        let total = 0;
                        for (const dataPoint of metricsRes.metricData) {
                            total += dataPoint.sum || 0;
                        }
                        console.log(`📊 NetworkIn total: ${total} bytes (${(total / (1024**3)).toFixed(6)} GB)`);
                    }
                } catch (lightsailMetricsError) {
                    console.log(`❌ Lightsail native metrics error: ${lightsailMetricsError.message}`);
                }
                
                // Also try NetworkOut
                try {
                    const now = new Date();
                    const oneWeekAgo = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
                    
                    const metricsRes = await lightsailClient.send(new GetInstanceMetricDataCommand({
                        instanceName: instance.name,
                        metricName: 'NetworkOut',
                        period: 3600,
                        startTime: oneWeekAgo,
                        endTime: now,
                        statistics: ['Sum']
                    }));
                    
                    if (metricsRes.metricData && metricsRes.metricData.length > 0) {
                        let total = 0;
                        for (const dataPoint of metricsRes.metricData) {
                            total += dataPoint.sum || 0;
                        }
                        console.log(`📊 NetworkOut total: ${total} bytes (${(total / (1024**3)).toFixed(6)} GB)`);
                    }
                } catch (error) {
                    console.log(`❌ NetworkOut error: ${error.message}`);
                }
            }
        } catch (error) {
            console.log(`❌ Lightsail error: ${error.message}`);
        }
        console.log('');
    }

    console.log('🎯 ANALYSIS SUMMARY');
    console.log('==================');
    console.log('');
    console.log('This deep dive should reveal:');
    console.log('1. Whether we\'re missing API fields or endpoints');
    console.log('2. If the data exists but we\'re not accessing it correctly');
    console.log('3. Whether providers have different metric collection delays');
    console.log('4. If there are alternative APIs we should be using');
    console.log('');
    console.log('💡 Next steps based on findings:');
    console.log('1. If data exists: Fix our provider implementations');
    console.log('2. If no data: Verify downloads completed successfully');
    console.log('3. If API limitations: Document expected delays');
}

testProviderAPIs().catch(console.error);
