const AzureProvider = require('./providers/azure');
require('dotenv').config();

async function debugAzureSimple() {
    console.log('🔍 Simple Azure Bandwidth Debugging');
    console.log('===================================');
    console.log('');
    console.log('Testing why Azure shows only 9MB when you downloaded 1GB...');
    console.log('');

    const azureProvider = new AzureProvider(
        process.env.AZURE_CLIENT_ID,
        process.env.AZURE_CLIENT_SECRET,
        process.env.AZURE_TENANT_ID,
        process.env.AZURE_SUBSCRIPTION_ID
    );

    try {
        // Step 1: List VMs
        console.log('📋 Step 1: List Azure VMs');
        const vms = await azureProvider.listServers();
        
        if (vms.length === 0) {
            console.log('❌ No VMs found');
            return;
        }

        const vm = vms[0];
        console.log(`✅ Found VM: ${vm.name}`);
        console.log(`📊 VM ID: ${vm.id}`);
        console.log('');

        // Step 2: Test current implementation
        console.log('📋 Step 2: Test current Azure provider implementation');
        const usage = await azureProvider.getBandwidthUsage(vm.id);
        
        if (usage) {
            console.log(`📊 Current result: ${(usage.outgoingBytes / (1024**3)).toFixed(6)} GB`);
            console.log(`📊 Raw bytes: ${usage.outgoingBytes}`);
            console.log('');
        }

        // Step 3: Manual Azure Monitor query with different parameters
        console.log('📋 Step 3: Manual Azure Monitor queries');
        
        const { MetricsQueryClient } = require("@azure/monitor-query");
        const { DefaultAzureCredential } = require("@azure/identity");
        
        const credential = new DefaultAzureCredential();
        const metricsClient = new MetricsQueryClient(credential);

        const now = new Date();
        const timeRanges = [
            { name: 'Last 1 hour', hours: 1 },
            { name: 'Last 6 hours', hours: 6 },
            { name: 'Last 24 hours', hours: 24 },
            { name: 'Last 48 hours', hours: 48 }
        ];

        const metrics = [
            'Network Out Total',
            'Network In Total'
        ];

        for (const metricName of metrics) {
            console.log(`\n🔍 Testing metric: ${metricName}`);
            
            for (const range of timeRanges) {
                try {
                    const startTime = new Date(now.getTime() - (range.hours * 60 * 60 * 1000));
                    
                    console.log(`\n  📊 ${range.name} (${startTime.toLocaleString()} to ${now.toLocaleString()})`);
                    
                    const metricsResponse = await metricsClient.queryResource(
                        vm.id,
                        [metricName],
                        {
                            granularity: "PT1H" // 1 hour granularity
                            // No timespan = default (last hour)
                        }
                    );

                    if (metricsResponse.metrics && metricsResponse.metrics.length > 0) {
                        const metric = metricsResponse.metrics[0];
                        
                        if (metric.timeseries && metric.timeseries.length > 0) {
                            const timeseries = metric.timeseries[0];
                            
                            if (timeseries.data && timeseries.data.length > 0) {
                                let total = 0;
                                let maxValue = 0;
                                let dataPointCount = timeseries.data.length;
                                
                                console.log(`    📊 Data points found: ${dataPointCount}`);
                                
                                for (const dataPoint of timeseries.data) {
                                    const value = dataPoint.total || dataPoint.average || dataPoint.maximum || 0;
                                    total += value;
                                    if (value > maxValue) maxValue = value;
                                    
                                    // Log individual data points for debugging
                                    if (value > 0) {
                                        console.log(`      ${dataPoint.timeStamp}: ${value} bytes (${(value / (1024**2)).toFixed(2)} MB)`);
                                    }
                                }
                                
                                console.log(`    📊 Total: ${total} bytes (${(total / (1024**3)).toFixed(6)} GB)`);
                                console.log(`    📊 Max single point: ${maxValue} bytes (${(maxValue / (1024**2)).toFixed(2)} MB)`);
                                
                                if (total > 100 * 1024 * 1024) { // More than 100MB
                                    console.log(`    🎉 FOUND SIGNIFICANT TRAFFIC! ${(total / (1024**3)).toFixed(3)} GB`);
                                }
                            } else {
                                console.log(`    ⚠️  No data points in timeseries`);
                            }
                        } else {
                            console.log(`    ⚠️  No timeseries data`);
                        }
                    } else {
                        console.log(`    ❌ No metrics returned`);
                    }
                } catch (error) {
                    console.log(`    ❌ Error: ${error.message}`);
                }
            }
        }

        // Step 4: Try with explicit timespan
        console.log('\n📋 Step 4: Testing with explicit timespan');
        try {
            const start24h = new Date(now.getTime() - (24 * 60 * 60 * 1000));
            
            const explicitResponse = await metricsClient.queryResource(
                vm.id,
                ["Network In Total"],
                {
                    granularity: "PT1H",
                    timespan: `${start24h.toISOString()}/${now.toISOString()}`
                }
            );

            if (explicitResponse.metrics && explicitResponse.metrics.length > 0) {
                const metric = explicitResponse.metrics[0];
                if (metric.timeseries && metric.timeseries.length > 0) {
                    const timeseries = metric.timeseries[0];
                    if (timeseries.data && timeseries.data.length > 0) {
                        let total = 0;
                        for (const dataPoint of timeseries.data) {
                            total += dataPoint.total || 0;
                        }
                        console.log(`📊 24h Network In Total with explicit timespan: ${total} bytes (${(total / (1024**3)).toFixed(6)} GB)`);
                        
                        if (total > 500 * 1024 * 1024) { // More than 500MB
                            console.log(`🎉 FOUND YOUR 1GB DOWNLOAD! ${(total / (1024**3)).toFixed(3)} GB`);
                        }
                    }
                }
            }
        } catch (error) {
            console.log(`❌ Explicit timespan error: ${error.message}`);
        }

    } catch (error) {
        console.error('❌ Debug failed:', error.message);
    }

    console.log('\n🎯 ANALYSIS');
    console.log('===========');
    console.log('');
    console.log('💡 Key insights:');
    console.log('1. Downloads (curl) should appear in "Network In Total"');
    console.log('2. Current provider only checks "Network Out Total"');
    console.log('3. Time range might be too narrow (default = last hour)');
    console.log('4. Azure metrics might have delays');
    console.log('');
    console.log('🔧 Potential fixes:');
    console.log('1. Check "Network In Total" instead of "Network Out Total"');
    console.log('2. Use longer time range (24 hours)');
    console.log('3. Sum all data points properly');
}

debugAzureSimple();
