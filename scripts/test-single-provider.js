const axios = require('axios');
require('dotenv').config();

// Import all providers
const HetznerProvider = require('./providers/hetzner');
const VultrProvider = require('./providers/vultr');
const LinodeProvider = require('./providers/linode');
const DigitalOceanProvider = require('./providers/digitalocean');
const AWSLightsailProvider = require('./providers/aws-lightsail');
const AzureProvider = require('./providers/azure');
const GCPProvider = require('./providers/gcp');

async function testSingleProvider(providerName) {
    console.log(`🔍 Testing ${providerName} Provider in Detail...\n`);

    let provider;
    let configured = false;

    switch (providerName.toLowerCase()) {
        case 'hetzner':
            provider = new HetznerProvider(process.env.HCLOUD_TOKEN);
            configured = !!process.env.HCLOUD_TOKEN;
            break;
        case 'vultr':
            provider = new VultrProvider(process.env.VULTR_API_KEY);
            configured = !!process.env.VULTR_API_KEY;
            break;
        case 'linode':
            provider = new LinodeProvider(process.env.LINODE_TOKEN);
            configured = !!process.env.LINODE_TOKEN;
            break;
        case 'digitalocean':
            provider = new DigitalOceanProvider(process.env.DIGITALOCEAN_TOKEN);
            configured = !!process.env.DIGITALOCEAN_TOKEN;
            break;
        case 'lightsail':
        case 'aws':
            provider = new AWSLightsailProvider(
                process.env.AWS_ACCESS_KEY_ID,
                process.env.AWS_SECRET_ACCESS_KEY,
                process.env.AWS_REGION || 'us-east-1'
            );
            configured = !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY);
            break;
        case 'azure':
            provider = new AzureProvider(
                process.env.AZURE_CLIENT_ID,
                process.env.AZURE_CLIENT_SECRET,
                process.env.AZURE_TENANT_ID,
                process.env.AZURE_SUBSCRIPTION_ID
            );
            configured = !!(process.env.AZURE_CLIENT_ID && process.env.AZURE_CLIENT_SECRET);
            break;
        case 'gcp':
            provider = new GCPProvider(
                process.env.GCP_PROJECT_ID,
                process.env.GCP_CREDENTIALS_FILE
            );
            configured = !!(process.env.GCP_PROJECT_ID && process.env.GCP_CREDENTIALS_FILE);
            break;
        default:
            console.error(`❌ Unknown provider: ${providerName}`);
            console.log('Available providers: hetzner, vultr, linode, digitalocean, lightsail, azure, gcp');
            return;
    }

    if (!configured) {
        console.log(`❌ ${providerName}: Not configured (missing credentials)`);
        return;
    }

    try {
        // Test 1: List servers
        console.log(`📋 Step 1: Listing ${providerName} servers...`);
        const servers = await provider.listServers();
        
        if (servers.length === 0) {
            console.log(`⚠️  ${providerName}: No servers found`);
            return;
        }

        console.log(`✅ Found ${servers.length} server(s):`);
        servers.forEach((server, index) => {
            console.log(`  ${index + 1}. ${server.name} (ID: ${server.id})`);
            if (server.state) console.log(`     State: ${server.state}`);
            if (server.bundleId) console.log(`     Bundle: ${server.bundleId}`);
            if (server.zone) console.log(`     Zone: ${server.zone}`);
        });

        // Test each server
        for (const [index, server] of servers.entries()) {
            console.log(`\n📊 Step 2.${index + 1}: Testing bandwidth for ${server.name}`);
            console.log(`${'='.repeat(50)}`);
            
            try {
                let usage;
                
                // Handle special cases for providers that need extra parameters
                if (providerName.toLowerCase() === 'gcp' && server.zone && server.numericId) {
                    console.log(`🔧 Using GCP-specific parameters: zone=${server.zone}, numericId=${server.numericId}`);
                    usage = await provider.getBandwidthUsage(server.id, server.zone, server.numericId);
                } else if ((providerName.toLowerCase() === 'lightsail' || providerName.toLowerCase() === 'aws') && server.region) {
                    console.log(`🔧 Using Lightsail-specific parameters: region=${server.region}`);
                    usage = await provider.getBandwidthUsage(server.id, server.region);
                } else {
                    usage = await provider.getBandwidthUsage(server.id);
                }
                
                if (usage) {
                    const usageGB = usage.outgoingBytes / (1024 ** 3);
                    const limitGB = usage.includedBytes / (1024 ** 3);
                    
                    console.log(`✅ Bandwidth data retrieved successfully:`);
                    console.log(`   Current usage: ${usageGB.toFixed(6)} GB`);
                    console.log(`   Transfer limit: ${limitGB.toFixed(0)} GB`);
                    console.log(`   Usage percentage: ${limitGB > 0 ? ((usageGB / limitGB) * 100).toFixed(4) : 0}%`);
                    console.log(`   Raw outgoing bytes: ${usage.outgoingBytes}`);
                    console.log(`   Raw included bytes: ${usage.includedBytes}`);
                    
                    if (usageGB === 0) {
                        console.log(`ℹ️  Zero usage is normal for new instances or instances with no recent traffic`);
                        console.log(`💡 To generate test traffic, SSH into the instance and run:`);
                        console.log(`   curl -o /dev/null http://speedtest.tele2.net/100MB.zip`);
                    } else {
                        console.log(`🎉 Non-zero usage detected! Monitoring is working correctly.`);
                    }
                } else {
                    console.log(`❌ Failed to get bandwidth usage (returned null)`);
                }
            } catch (bandwidthError) {
                console.log(`❌ Bandwidth error:`, bandwidthError.message);
                console.log(`🔍 Error details:`, bandwidthError);
            }
        }
        
        console.log(`\n✅ ${providerName} provider test completed successfully!`);
        
    } catch (error) {
        console.log(`❌ ${providerName} provider error:`, error.message);
        console.log(`🔍 Full error:`, error);
    }
}

// Get provider name from command line argument
const providerName = process.argv[2];

if (!providerName) {
    console.log('Usage: node test-single-provider.js <provider>');
    console.log('');
    console.log('Available providers:');
    console.log('  hetzner');
    console.log('  vultr');
    console.log('  linode');
    console.log('  digitalocean');
    console.log('  lightsail (or aws)');
    console.log('  azure');
    console.log('  gcp');
    console.log('');
    console.log('Example: node test-single-provider.js linode');
    process.exit(1);
}

testSingleProvider(providerName).catch(console.error);
