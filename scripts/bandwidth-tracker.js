const fs = require('fs');
const path = require('path');

class BandwidthTracker {
    constructor() {
        // Use environment variable for Docker, fallback to local file
        const dataDir = process.env.BANDWIDTH_DATA_FILE ? path.dirname(process.env.BANDWIDTH_DATA_FILE) : __dirname;
        const dataFile = process.env.BANDWIDTH_DATA_FILE || 'bandwidth-usage.json';
        this.dataFile = path.isAbsolute(dataFile) ? dataFile : path.join(dataDir, dataFile);
        this.monthlyLimitGB = parseFloat(process.env.MONTHLY_BANDWIDTH_LIMIT_GB || "20480");
        this.dailyBurstLimitGB = parseFloat(process.env.DAILY_BURST_LIMIT_GB || "10");
        this.burstWindowHours = parseFloat(process.env.BURST_WINDOW_HOURS || "6");
        this.earlyWarningThresholdGB = parseFloat(process.env.EARLY_WARNING_THRESHOLD_GB || "5");
        this.enableTermination = process.env.ENABLE_INSTANCE_TERMINATION === "true";
        
        this.loadData();
    }

    loadData() {
        try {
            if (fs.existsSync(this.dataFile)) {
                const data = JSON.parse(fs.readFileSync(this.dataFile, 'utf8'));
                this.usageData = data;
            } else {
                this.usageData = {};
            }
        } catch (error) {
            console.error('Error loading bandwidth data:', error.message);
            this.usageData = {};
        }
    }

    saveData() {
        try {
            fs.writeFileSync(this.dataFile, JSON.stringify(this.usageData, null, 2));
        } catch (error) {
            console.error('Error saving bandwidth data:', error.message);
        }
    }

    getInstanceKey(provider, instanceId) {
        return `${provider}-${instanceId}`;
    }

    getCurrentMonth() {
        const now = new Date();
        return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    }

    getCurrentDay() {
        const now = new Date();
        return now.toISOString().split('T')[0]; // YYYY-MM-DD
    }

    recordUsage(provider, instanceId, instanceName, usageGB) {
        const key = this.getInstanceKey(provider, instanceId);
        const month = this.getCurrentMonth();
        const day = this.getCurrentDay();
        const timestamp = new Date().toISOString();

        if (!this.usageData[key]) {
            this.usageData[key] = {
                provider,
                instanceId,
                instanceName,
                months: {}
            };
        }

        if (!this.usageData[key].months[month]) {
            this.usageData[key].months[month] = {
                totalGB: 0,
                days: {},
                burstEvents: []
            };
        }

        if (!this.usageData[key].months[month].days[day]) {
            this.usageData[key].months[month].days[day] = {
                totalGB: 0,
                readings: []
            };
        }

        // Record the reading
        this.usageData[key].months[month].days[day].readings.push({
            timestamp,
            usageGB
        });

        // Update daily total (use the latest reading as current total)
        this.usageData[key].months[month].days[day].totalGB = usageGB;

        // Update monthly total (sum of all daily totals)
        this.usageData[key].months[month].totalGB = Object.values(
            this.usageData[key].months[month].days
        ).reduce((sum, day) => Math.max(sum, day.totalGB), 0);

        this.saveData();
    }

    analyzeUsage(provider, instanceId, instanceName, currentUsageGB) {
        this.recordUsage(provider, instanceId, instanceName, currentUsageGB);
        
        const key = this.getInstanceKey(provider, instanceId);
        const month = this.getCurrentMonth();
        const day = this.getCurrentDay();
        
        const monthlyData = this.usageData[key]?.months[month];
        const dailyData = monthlyData?.days[day];
        
        if (!monthlyData || !dailyData) {
            return { action: 'none', reason: 'No data available' };
        }

        const analysis = {
            currentUsageGB,
            dailyUsageGB: dailyData.totalGB,
            monthlyUsageGB: monthlyData.totalGB,
            monthlyLimitGB: this.monthlyLimitGB,
            dailyBurstLimitGB: this.dailyBurstLimitGB,
            earlyWarningThresholdGB: this.earlyWarningThresholdGB,
            enableTermination: this.enableTermination,
            action: 'none',
            reason: '',
            warnings: []
        };

        // Check early warning threshold
        if (currentUsageGB >= this.earlyWarningThresholdGB) {
            analysis.warnings.push(`Daily usage (${currentUsageGB.toFixed(2)} GB) exceeds early warning threshold (${this.earlyWarningThresholdGB} GB)`);
        }

        // Check daily burst limit
        if (currentUsageGB >= this.dailyBurstLimitGB) {
            analysis.warnings.push(`Daily usage (${currentUsageGB.toFixed(2)} GB) exceeds daily burst limit (${this.dailyBurstLimitGB} GB)`);
            
            // Check if this is a sustained burst (needs termination)
            if (this.isSustainedBurst(key, month)) {
                analysis.action = 'terminate';
                analysis.reason = `Sustained burst detected over ${this.burstWindowHours} hours`;
            } else {
                analysis.action = 'warn';
                analysis.reason = 'Daily burst limit exceeded but within burst window';
                // Record burst event
                this.recordBurstEvent(key, month, currentUsageGB);
            }
        }

        // Check monthly limit (always terminate if exceeded)
        const projectedMonthlyUsage = this.projectMonthlyUsage(monthlyData);
        if (projectedMonthlyUsage >= this.monthlyLimitGB) {
            analysis.action = 'terminate';
            analysis.reason = `Projected monthly usage (${projectedMonthlyUsage.toFixed(2)} GB) exceeds limit (${this.monthlyLimitGB} GB)`;
            analysis.warnings.push(analysis.reason);
        }

        return analysis;
    }

    recordBurstEvent(key, month, usageGB) {
        const timestamp = new Date().toISOString();
        this.usageData[key].months[month].burstEvents.push({
            timestamp,
            usageGB
        });
        this.saveData();
    }

    isSustainedBurst(key, month) {
        const burstEvents = this.usageData[key]?.months[month]?.burstEvents || [];
        if (burstEvents.length === 0) return false;

        const now = new Date();
        const burstWindowMs = this.burstWindowHours * 60 * 60 * 1000;
        
        // Check if we have burst events within the burst window
        const recentBursts = burstEvents.filter(event => {
            const eventTime = new Date(event.timestamp);
            return (now - eventTime) <= burstWindowMs;
        });

        // If we have multiple burst events in the window, it's sustained
        return recentBursts.length >= 2;
    }

    projectMonthlyUsage(monthlyData) {
        const now = new Date();
        const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
        const dayOfMonth = now.getDate();
        
        // Simple projection: current usage * (days in month / current day)
        return monthlyData.totalGB * (daysInMonth / dayOfMonth);
    }

    getUsageReport(provider, instanceId) {
        const key = this.getInstanceKey(provider, instanceId);
        const month = this.getCurrentMonth();
        
        const data = this.usageData[key];
        if (!data) return null;

        const monthlyData = data.months[month];
        if (!monthlyData) return null;

        return {
            instanceName: data.instanceName,
            provider: data.provider,
            monthlyUsageGB: monthlyData.totalGB,
            monthlyLimitGB: this.monthlyLimitGB,
            dailyBurstLimitGB: this.dailyBurstLimitGB,
            burstEvents: monthlyData.burstEvents.length,
            projectedMonthlyUsage: this.projectMonthlyUsage(monthlyData)
        };
    }
}

module.exports = BandwidthTracker;
