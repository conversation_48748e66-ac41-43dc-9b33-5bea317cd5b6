from datetime import datetime, timedelta, timezone
from decimal import Decimal
from math import ceil
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.core.config import get_settings
from app.models.user import User
from app.models.deployment import Deployment
from app.models.transaction import Transaction


def _process_deployments_billing(deployments_to_process, now, db, settings):
    """
    Shared billing logic for processing a list of deployments.
    
    Args:
        deployments_to_process: List of deployment objects to process
        now: Current datetime for billing calculations
        db: Database session
        settings: Application settings
        
    Returns:
        Dictionary with billing summary information
    """
    summary = {"charged_users": 0, "terminated_deployments": 0, "errors": []}
    users_to_check = set()

    # Get billing interval from settings
    billing_interval_minutes = getattr(settings, 'BILLING_INTERVAL_MINUTES', 60)
    billing_interval = timedelta(minutes=billing_interval_minutes)

    for d in deployments_to_process:
        try:
            # This logic implements a configurable billing interval model,
            # billing for discrete time blocks without double-charging.

            # Ensure the deployment's creation time is timezone-aware to prevent comparison errors.
            created_at_utc = d.created_at
            if created_at_utc.tzinfo is None:
                created_at_utc = created_at_utc.replace(tzinfo=timezone.utc)

            # Determine the starting interval for the check.
            # For proper billing, we use 24-hour periods from deployment creation time
            if billing_interval_minutes == 1440:  # Daily billing (24-hour periods)
                # For daily billing, use 24-hour periods from deployment creation time
                if d.last_billed_at:
                    # Start from 24 hours after the last billing period started
                    last_billed_utc = d.last_billed_at.replace(tzinfo=timezone.utc) if d.last_billed_at.tzinfo is None else d.last_billed_at
                    # Since last_billed_at stores the end of the period (start + 24h - 1s),
                    # the next period starts 1 second after last_billed_at
                    current_interval_to_check = last_billed_utc + timedelta(seconds=1)
                else:
                    # Start from the deployment creation time for the first billing period
                    current_interval_to_check = created_at_utc
            elif billing_interval_minutes == 60:  # Hourly billing
                # For hourly billing, align to hour boundaries
                if d.last_billed_at:
                    # Start from the hour after the last billed hour
                    last_billed_utc = d.last_billed_at.replace(tzinfo=timezone.utc) if d.last_billed_at.tzinfo is None else d.last_billed_at
                    next_hour = last_billed_utc.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                    current_interval_to_check = next_hour
                else:
                    # Start from the hour when deployment was created
                    current_interval_to_check = created_at_utc.replace(minute=0, second=0, microsecond=0)
            else:
                # For other intervals, use the original logic
                if d.last_billed_at:
                    start_to_check_from = d.last_billed_at.replace(tzinfo=timezone.utc) if d.last_billed_at.tzinfo is None else d.last_billed_at
                    current_interval_to_check = start_to_check_from + billing_interval
                else:
                    current_interval_to_check = created_at_utc

            now_utc = now or datetime.now(timezone.utc)
            last_successfully_billed_interval = None

            # Loop through each billing interval from our starting point up to the current time.
            while current_interval_to_check < now_utc:
                # Skip if the deployment was created *after* this entire interval block ended.
                if created_at_utc > current_interval_to_check + billing_interval:
                    current_interval_to_check += billing_interval
                    continue

                # This is a billable interval.
                hourly_cost_cents = d.cost or 0
                
                # Calculate the charge based on the billing interval
                # cost is stored as hourly cost in cents, so we need to adjust for the billing interval
                interval_multiplier = Decimal(billing_interval_minutes) / Decimal(60)  # Convert to hour fraction
                charge_eur = (Decimal(hourly_cost_cents) / Decimal(100)) * interval_multiplier

                if charge_eur <= Decimal('0.00'):
                    current_interval_to_check += billing_interval
                    continue
                
                user = d.user
                if not user:
                    break  # Exit while loop for this deployment

                # Use the new billing service to charge user (uses credits first, then regular balance)
                from app.services.billing_service import BillingService
                billing_service = BillingService(db)

                # Create descriptive billing message with full period
                if billing_interval_minutes == 1440:  # Daily billing (24-hour periods)
                    period_end = current_interval_to_check + billing_interval
                    description = f"Usage charge for deployment #{d.id} (24h period: {current_interval_to_check.strftime('%Y-%m-%d %H:%M:%S')} to {period_end.strftime('%Y-%m-%d %H:%M:%S')} UTC)"
                else:
                    description = f"Deployment #{d.id} usage (Interval: {current_interval_to_check.strftime('%Y-%m-%d %H:%M')})"

                charge_result = billing_service.charge_user(
                    user,
                    charge_eur,
                    description
                )

                if not charge_result["success"]:
                    print(f"[BILLING ERROR] Failed to charge user {user.id}: {charge_result.get('error', 'Unknown error')}")
                    break  # Exit while loop for this deployment

                # Log the successful charge
                credits_used = charge_result.get("credits_used", Decimal("0"))
                regular_used = charge_result.get("regular_balance_used", Decimal("0"))
                if credits_used > 0:
                    print(f"[BILLING] Charged user {user.id}: €{charge_eur:.4f} (€{credits_used:.4f} from credits, €{regular_used:.4f} from balance)")
                else:
                    print(f"[BILLING] Charged user {user.id}: €{charge_eur:.4f} from balance")

                users_to_check.add(user.id)
                
                # Store the end of the billing period as last_billed_at
                # This ensures the next billing cycle starts from the correct point
                if billing_interval_minutes == 1440:  # Daily billing (24-hour periods)
                    # For daily billing, store the end of the 24-hour period
                    last_successfully_billed_interval = current_interval_to_check + billing_interval - timedelta(seconds=1)
                elif billing_interval_minutes == 60:  # Hourly billing
                    # For hourly billing, store the end of the hour (59:59)
                    last_successfully_billed_interval = current_interval_to_check.replace(minute=59, second=59)
                else:
                    # For other intervals, store the end of the interval
                    last_successfully_billed_interval = current_interval_to_check + billing_interval - timedelta(seconds=1)

                current_interval_to_check += billing_interval

            if last_successfully_billed_interval:
                d.last_billed_at = last_successfully_billed_interval
                db.add(d)
            
            db.commit()

        except Exception as e:
            db.rollback()
            error_msg = f"Error billing deployment {d.id}: {e}"
            print(error_msg)
            summary["errors"].append(error_msg)
    
    summary["charged_users"] = len(users_to_check)

    # --- Termination Check ---
    # Check balances for all users who were charged
    for user_id in users_to_check:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            continue

        # Check total balance including credits
        from app.services.billing_service import BillingService
        billing_service = BillingService(db)
        balance_info = billing_service.get_user_total_balance(user)

        total_balance_float = float(balance_info["total_balance"])
        regular_balance_float = float(balance_info["regular_balance"])

        # Check for low balance warning (use total balance including credits)
        if 0 < total_balance_float < settings.MIN_BALANCE_TO_DEPLOY:
            print(f"[LOW BALANCE WARNING] User {user.id} total balance is low: {total_balance_float:.2f} EUR "
                  f"(Regular: {regular_balance_float:.2f}, Credits: {total_balance_float - regular_balance_float:.2f})")
            # Send at most once per 24h
            try:
                last = getattr(user, 'last_low_balance_email_at', None)
                should_send = last is None or (datetime.now(timezone.utc) - last.replace(tzinfo=timezone.utc)).total_seconds() > 24*3600
                if should_send:
                    from app.services.email_service import send_low_balance_warning
                    send_low_balance_warning(user.email, user.username, total_balance_float, settings.MIN_BALANCE_TO_DEPLOY)
                    user.last_low_balance_email_at = datetime.now(timezone.utc)
                    db.add(user)
                    db.commit()
            except Exception as e:
                print(f"[EMAIL] Failed to send low balance warning: {e}")

        # Terminate if total balance is zero or negative
        if total_balance_float <= 0:
            print(f"[TERMINATION] User {user.id} total balance is {total_balance_float:.2f} EUR. Initiating termination.")
            # Get all billable deployments for this user, most expensive first
            # Include both ACTIVE and READY status as they are both operational and billable
            deployments_to_terminate = db.query(Deployment).filter(
                Deployment.user_id == user.id,
                Deployment.status.in_(['ACTIVE', 'READY']),
                Deployment.deleted_at.is_(None)
            ).order_by(Deployment.cost.desc()).all()

            from app.core.deployment.deployment_manager import DeploymentManager
            manager = DeploymentManager()
            for dep in deployments_to_terminate:
                try:
                    print(f"Terminating deployment {dep.id} for user {user.id}...")
                    manager.destroy(int(dep.id))

                    dep.deleted_at = now
                    # Status will be set to DESTROYING by the destroy method

                    term_tx = Transaction(
                        user_id=user.id,
                        amount=Decimal('0'),
                        type="termination",
                        description=f"Deployment #{dep.id} terminated due to insufficient balance."
                    )
                    db.add(dep)
                    db.add(term_tx)
                    db.commit()

                    summary["terminated_deployments"] += 1
                    print(f"Termination of deployment {dep.id} successful.")

                    # Send termination notice once per cycle for affected user
                    try:
                        from app.services.email_service import send_termination_notice
                        # collect all terminated deployment ids for the user in this pass
                        terminated_ids = [dep.id]
                        last = getattr(user, 'last_termination_email_at', None)
                        should_send = last is None or (now - (last if last.tzinfo else last.replace(tzinfo=timezone.utc))).total_seconds() > 3600
                        if should_send:
                            send_termination_notice(user.email, user.username, terminated_ids, float(user.balance or 0))
                            user.last_termination_email_at = now
                            db.add(user)
                            db.commit()
                    except Exception as e:
                        print(f"[EMAIL] Failed to send termination notice: {e}")

                    # Re-fetch user and check if balance is now positive
                    db.refresh(user)
                    if (user.balance or 0) > 0:
                        print(f"User {user.id} balance is now positive. Stopping termination cycle for this user.")
                        break

                except Exception as e:
                    db.rollback()
                    error_msg = f"[AUTO-TERMINATE] Failed to destroy deployment {dep.id}: {e}"
                    print(error_msg)
                    summary["errors"].append(error_msg)
    
    return summary


def run_billing_for_user_deployment(user_id: int, deployment_id: int | None = None, now: datetime | None = None) -> dict:
    """
    Runs billing for a specific user's deployments or a specific deployment.
    
    This function can be called immediately when a deployment is created to ensure
    users see up-to-date billing calculations.
    
    Args:
        user_id: The user ID to run billing for
        deployment_id: Optional specific deployment ID to bill (if None, bills all user's deployments)
        now: An optional datetime to override the current time, used for testing
        
    Returns:
        A summary dictionary logging the results of the billing operation.
    """
    settings = get_settings()
    now = now or datetime.now(timezone.utc)
    db: Session = SessionLocal()
    summary = {"charged_users": 0, "terminated_deployments": 0, "errors": []}
    
    try:
        # Get deployments to bill - include both ACTIVE and READY as they are both operational
        query = db.query(Deployment).filter(
            Deployment.user_id == user_id,
            Deployment.status.in_(['ACTIVE', 'READY']),
            Deployment.deleted_at.is_(None)
        )
        
        if deployment_id:
            query = query.filter(Deployment.id == deployment_id)
            
        deployments_to_bill = query.all()
        
        if not deployments_to_bill:
            return summary
            
        # Use the same billing logic as the main billing cycle
        result = _process_deployments_billing(deployments_to_bill, now, db, settings)
        summary.update(result)
        
        return summary
    finally:
        db.close()


def run_billing_cycle(now: datetime | None = None) -> dict:
    """
    Runs the billing cycle for all billable deployments.

    This function is designed to be run periodically via a cron job or scheduler.
    It is idempotent and robust, ensuring that users are billed accurately even if runs are missed or repeated.

    - Calculates charges for operational deployments ('ACTIVE' and 'READY' status) based on configurable billing intervals since the last billing timestamp.
    - Deducts charges from user balances.
    - Automatically terminates deployments if a user's balance falls to or below zero.
    - Creates detailed transaction records for all billing and termination events.

    Args:
        now: An optional datetime to override the current time, used for testing.

    Returns:
        A summary dictionary logging the results of the cycle.
    """
    settings = get_settings()
    now = now or datetime.now(timezone.utc)
    db: Session = SessionLocal()
    
    try:
        # First, expire old credits
        from app.services.billing_service import BillingService
        billing_service = BillingService(db)
        expiry_result = billing_service.expire_credits()
        if expiry_result["expired_referral_credits"] > 0 or expiry_result["expired_signup_credits"] > 0:
            print(f"[CREDIT EXPIRY] Expired {expiry_result['expired_referral_credits']} referral credits "
                  f"and {expiry_result['expired_signup_credits']} signup credits")

        # Get all deployments that are billable (operational) and not marked as deleted
        active_deployments = db.query(Deployment).filter(
            Deployment.status.in_(['ACTIVE', 'READY']),
            Deployment.deleted_at.is_(None)
        ).all()

        # Use the shared billing logic
        summary = _process_deployments_billing(active_deployments, now, db, settings)
        summary.update(expiry_result)

        return summary
    finally:
        db.close()


if __name__ == "__main__":
    print("Starting billing cycle...")
    result = run_billing_cycle()
    print(f"Billing cycle complete: {result}")

