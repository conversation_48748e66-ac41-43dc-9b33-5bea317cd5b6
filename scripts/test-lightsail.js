const { LightsailClient, GetInstancesCommand } = require("@aws-sdk/client-lightsail");
require('dotenv').config();

const AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID;
const AWS_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY;
const AWS_REGION = process.env.AWS_REGION || 'us-east-1';

async function testLightsailAPI() {
    console.log('🔍 Testing AWS Lightsail API...');
    console.log('Access Key configured:', !!AWS_ACCESS_KEY_ID);
    console.log('Secret Key configured:', !!AWS_SECRET_ACCESS_KEY);
    console.log('Region:', AWS_REGION);
    
    if (!AWS_ACCESS_KEY_ID || !AWS_SECRET_ACCESS_KEY) {
        console.error('❌ AWS credentials not configured');
        return;
    }

    try {
        const lightsailClient = new LightsailClient({
            region: AWS_REGION,
            credentials: {
                accessKeyId: AWS_ACCESS_KEY_ID,
                secretAccessKey: AWS_SECRET_ACCESS_KEY
            }
        });

        // Test 1: List all instances
        console.log('\n📋 Testing: List all Lightsail instances');
        const command = new GetInstancesCommand({});
        const data = await lightsailClient.send(command);
        
        console.log('✅ API call successful');
        console.log('Response structure:', Object.keys(data));
        
        if (data.instances) {
            console.log(`✅ Found ${data.instances.length} Lightsail instances`);
            
            if (data.instances.length === 0) {
                console.log('⚠️  No Lightsail instances found in this region');
                console.log('💡 Make sure you have Lightsail instances created in region:', AWS_REGION);
                console.log('💡 Try checking other regions if your instances are elsewhere');
            } else {
                data.instances.forEach((instance, index) => {
                    console.log(`\n📊 Instance ${index + 1}:`);
                    console.log(`  Name: ${instance.name}`);
                    console.log(`  State: ${instance.state?.name || 'unknown'}`);
                    console.log(`  Bundle: ${instance.bundleId}`);
                    console.log(`  Region: ${instance.location?.regionName}`);
                    console.log(`  AZ: ${instance.location?.availabilityZone}`);
                    console.log(`  Public IP: ${instance.publicIpAddress || 'none'}`);
                    console.log(`  Private IP: ${instance.privateIpAddress || 'none'}`);
                });
            }
        } else {
            console.log('⚠️  No instances property in response');
        }

        // Test 2: Try different regions if no instances found
        if (!data.instances || data.instances.length === 0) {
            console.log('\n🔄 Testing other common regions...');
            const regions = ['us-west-2', 'eu-west-1', 'ap-southeast-1', 'us-east-2'];
            
            for (const region of regions) {
                if (region === AWS_REGION) continue; // Skip the region we already tested
                
                try {
                    console.log(`\n📍 Checking region: ${region}`);
                    const regionalClient = new LightsailClient({
                        region: region,
                        credentials: {
                            accessKeyId: AWS_ACCESS_KEY_ID,
                            secretAccessKey: AWS_SECRET_ACCESS_KEY
                        }
                    });
                    
                    const regionalData = await regionalClient.send(new GetInstancesCommand({}));
                    if (regionalData.instances && regionalData.instances.length > 0) {
                        console.log(`✅ Found ${regionalData.instances.length} instances in ${region}!`);
                        regionalData.instances.forEach(instance => {
                            console.log(`  - ${instance.name} (${instance.state?.name})`);
                        });
                        console.log(`💡 Update your AWS_REGION to: ${region}`);
                    } else {
                        console.log(`  No instances in ${region}`);
                    }
                } catch (regionError) {
                    console.log(`  Error checking ${region}:`, regionError.message);
                }
            }
        }

    } catch (error) {
        console.error('❌ Lightsail API Test failed:', error.name, error.message);
        
        if (error.name === 'UnauthorizedOperation' || error.name === 'AccessDenied') {
            console.error('💡 Check your AWS credentials and permissions');
            console.error('💡 Make sure your IAM user has Lightsail permissions');
        } else if (error.name === 'InvalidUserID.NotFound') {
            console.error('💡 Check your AWS Access Key ID');
        } else if (error.name === 'SignatureDoesNotMatch') {
            console.error('💡 Check your AWS Secret Access Key');
        }
    }
}

testLightsailAPI();
