{"name": "cloud-bandwidth-monitor", "version": "1.0.0", "description": "Multi-cloud bandwidth monitoring and auto-shutdown tool", "main": "monitor-refactored.js", "scripts": {"start": "node monitor-refactored.js", "start-legacy": "node monitor.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["cloud", "monitoring", "bandwidth", "<PERSON><PERSON><PERSON>", "vultr", "linode", "aws", "azure", "gcp", "digitalocean"], "author": "", "license": "MIT", "dependencies": {"@aws-sdk/client-cloudwatch": "^3.879.0", "@aws-sdk/client-ec2": "^3.450.0", "@aws-sdk/client-lightsail": "^3.879.0", "@azure/arm-compute": "^23.0.0", "@azure/arm-monitor": "^7.0.0", "@azure/arm-network": "^33.1.0", "@azure/arm-resources": "^5.2.0", "@azure/identity": "^4.0.1", "@azure/monitor-query": "^1.3.3", "@azure/monitor-query-metrics": "^1.0.0", "@google-cloud/compute": "^4.7.0", "@google-cloud/monitoring": "^5.3.0", "axios": "^1.6.0", "discord.js": "^14.22.1", "dotenv": "^16.3.1"}}