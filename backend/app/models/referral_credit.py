from sqlalchemy import Column, Integer, String, Numeric, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
from app.db.base import BaseModel

class ReferralCredit(BaseModel):
    __tablename__ = "referral_credits"

    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    amount = Column(Numeric(12, 2), nullable=False)  # Credit amount in EUR
    credit_type = Column(String, nullable=False)  # 'referrer' or 'referee'
    referral_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # The other user in the referral
    expires_at = Column(DateTime, nullable=True)  # When this credit expires (NULL = no expiry)
    used_amount = Column(Numeric(12, 2), nullable=False, default=0)  # How much has been used
    is_active = Column(Boolean, nullable=False, default=True)  # Whether this credit is still active

    user = relationship("User", foreign_keys=[user_id], overlaps="referral_credits")
    referral_user = relationship("User", foreign_keys=[referral_user_id])

    @property
    def remaining_amount(self):
        """Calculate remaining credit amount."""
        return self.amount - self.used_amount

    @property
    def is_expired(self):
        """Check if this credit has expired."""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at

    @property
    def is_usable(self):
        """Check if this credit can be used."""
        return (
            self.is_active and 
            not self.is_expired and 
            self.remaining_amount > 0
        )
