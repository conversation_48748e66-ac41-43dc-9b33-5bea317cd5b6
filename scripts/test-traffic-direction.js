const axios = require('axios');
require('dotenv').config();

async function testTrafficDirection() {
    console.log('🔍 Testing Traffic Direction (Incoming vs Outgoing)');
    console.log('==================================================');
    console.log('');
    console.log('This script tests whether your 1GB downloads show up as');
    console.log('incoming or outgoing traffic on different providers.');
    console.log('');

    // Test DigitalOcean - Check both directions
    if (process.env.DIGITALOCEAN_TOKEN) {
        console.log('🔧 DIGITALOCEAN - BOTH DIRECTIONS');
        console.log('=================================');
        try {
            const dropletsRes = await axios.get('https://api.digitalocean.com/v2/droplets', {
                headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` }
            });
            
            if (dropletsRes.data.droplets.length > 0) {
                const droplet = dropletsRes.data.droplets[0];
                console.log(`📊 Droplet: ${droplet.name} (ID: ${droplet.id})`);
                
                const now = Math.floor(Date.now() / 1000);
                const start24h = now - (24 * 3600);
                
                // Test OUTBOUND traffic
                try {
                    const outboundRes = await axios.get(
                        `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=public&direction=outbound&start=${start24h}&end=${now}`,
                        { headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` } }
                    );
                    
                    let outboundTotal = 0;
                    if (outboundRes.data.data && outboundRes.data.data.result) {
                        for (const result of outboundRes.data.data.result) {
                            if (result.values) {
                                for (const [timestamp, value] of result.values) {
                                    outboundTotal += parseFloat(value) * 300;
                                }
                            }
                        }
                    }
                    console.log(`📤 OUTBOUND (24h): ${outboundTotal} bytes (${(outboundTotal / (1024**3)).toFixed(6)} GB)`);
                } catch (outError) {
                    console.log(`❌ Outbound error: ${outError.message}`);
                }
                
                // Test INBOUND traffic
                try {
                    const inboundRes = await axios.get(
                        `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=public&direction=inbound&start=${start24h}&end=${now}`,
                        { headers: { Authorization: `Bearer ${process.env.DIGITALOCEAN_TOKEN}` } }
                    );
                    
                    let inboundTotal = 0;
                    if (inboundRes.data.data && inboundRes.data.data.result) {
                        for (const result of inboundRes.data.data.result) {
                            if (result.values) {
                                for (const [timestamp, value] of result.values) {
                                    inboundTotal += parseFloat(value) * 300;
                                }
                            }
                        }
                    }
                    console.log(`📥 INBOUND (24h): ${inboundTotal} bytes (${(inboundTotal / (1024**3)).toFixed(6)} GB)`);
                    
                    if (inboundTotal > 100 * 1024 * 1024) { // More than 100MB
                        console.log(`🎉 Found significant INBOUND traffic! Your download shows up here.`);
                    }
                } catch (inError) {
                    console.log(`❌ Inbound error: ${inError.message}`);
                }
            }
        } catch (error) {
            console.log(`❌ DigitalOcean error: ${error.message}`);
        }
        console.log('');
    }

    // Test AWS Lightsail - Check both directions
    if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
        console.log('🔧 AWS LIGHTSAIL - BOTH DIRECTIONS');
        console.log('==================================');
        try {
            const { LightsailClient, GetInstancesCommand } = require("@aws-sdk/client-lightsail");
            const { CloudWatchClient, GetMetricStatisticsCommand } = require("@aws-sdk/client-cloudwatch");
            
            const lightsailClient = new LightsailClient({
                region: process.env.AWS_REGION || 'us-east-1',
                credentials: {
                    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
                }
            });
            
            const cloudwatchClient = new CloudWatchClient({
                region: process.env.AWS_REGION || 'us-east-1',
                credentials: {
                    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
                }
            });
            
            const instancesRes = await lightsailClient.send(new GetInstancesCommand({}));
            
            if (instancesRes.instances && instancesRes.instances.length > 0) {
                const instance = instancesRes.instances[0];
                console.log(`📊 Instance: ${instance.name}`);
                
                const now = new Date();
                const start24h = new Date(now.getTime() - (24 * 60 * 60 * 1000));
                
                // Test NetworkOut (outbound)
                try {
                    const outRes = await cloudwatchClient.send(new GetMetricStatisticsCommand({
                        Namespace: 'AWS/Lightsail',
                        MetricName: 'NetworkOut',
                        Dimensions: [{ Name: 'InstanceName', Value: instance.name }],
                        StartTime: start24h,
                        EndTime: now,
                        Period: 3600,
                        Statistics: ['Sum']
                    }));
                    
                    const outTotal = outRes.Datapoints ? outRes.Datapoints.reduce((sum, dp) => sum + (dp.Sum || 0), 0) : 0;
                    console.log(`📤 NETWORK OUT (24h): ${outTotal} bytes (${(outTotal / (1024**3)).toFixed(6)} GB)`);
                } catch (outError) {
                    console.log(`❌ NetworkOut error: ${outError.message}`);
                }
                
                // Test NetworkIn (inbound)
                try {
                    const inRes = await cloudwatchClient.send(new GetMetricStatisticsCommand({
                        Namespace: 'AWS/Lightsail',
                        MetricName: 'NetworkIn',
                        Dimensions: [{ Name: 'InstanceName', Value: instance.name }],
                        StartTime: start24h,
                        EndTime: now,
                        Period: 3600,
                        Statistics: ['Sum']
                    }));
                    
                    const inTotal = inRes.Datapoints ? inRes.Datapoints.reduce((sum, dp) => sum + (dp.Sum || 0), 0) : 0;
                    console.log(`📥 NETWORK IN (24h): ${inTotal} bytes (${(inTotal / (1024**3)).toFixed(6)} GB)`);
                    
                    if (inTotal > 100 * 1024 * 1024) { // More than 100MB
                        console.log(`🎉 Found significant INBOUND traffic! Your download shows up here.`);
                    }
                } catch (inError) {
                    console.log(`❌ NetworkIn error: ${inError.message}`);
                }
            }
        } catch (error) {
            console.log(`❌ Lightsail error: ${error.message}`);
        }
        console.log('');
    }

    console.log('🎯 TRAFFIC DIRECTION ANALYSIS');
    console.log('=============================');
    console.log('');
    console.log('Understanding traffic direction:');
    console.log('');
    console.log('📥 INBOUND/INCOMING = Downloads TO your VPS');
    console.log('   - curl downloads from internet');
    console.log('   - SSH connections to your VPS');
    console.log('   - Web traffic to your VPS');
    console.log('');
    console.log('📤 OUTBOUND/OUTGOING = Uploads FROM your VPS');
    console.log('   - Uploading files from your VPS');
    console.log('   - Your VPS serving web content');
    console.log('   - API calls from your VPS');
    console.log('');
    console.log('💡 Your 1GB curl download should appear as INBOUND traffic!');
    console.log('');
    console.log('✅ Current working providers:');
    console.log('  - Vultr: ✅ 1.01 GB inbound detected');
    console.log('  - Linode: ✅ 1.00 GB account-level');
    console.log('  - Azure: ✅ 0.009 GB detected');
    console.log('  - Hetzner: ✅ 0.001 GB detected');
}

testTrafficDirection().catch(console.error);
