from sqlalchemy.orm import Session
from decimal import Decimal
from datetime import datetime, timedelta
import secrets
from app.models.user import User
from app.models.transaction import Transaction
from app.schemas.user import UserCreate
from app.core.security import get_password_hash
from app.services.email_service import send_verification_email
from app.services.referral_service import ReferralService
from app.services.signup_credit_service import SignupCreditService
from app.core.config import get_settings


def get_user(db: Session, user_id: int):
    return db.query(User).filter(User.id == user_id).first()


def get_user_by_email(db: Session, email: str):
    return db.query(User).filter(User.email == email).first()


def get_users(db: Session, skip: int = 0, limit: int = 100):
    return db.query(User).order_by(User.id).offset(skip).limit(limit).all()


def _maybe_send_verification(db: Session, user: User) -> None:
    settings = get_settings()
    if not settings.USE_EMAIL:
        return
    try:
        token = user.verification_token or secrets.token_urlsafe(32)
        user.verification_token = token
        user.verification_token_expires_at = datetime.utcnow() + timedelta(hours=24)
        user.verification_sent_at = datetime.utcnow()
        db.add(user)
        db.commit()
        send_verification_email(user.email, user.username, token)
    except Exception as e:
        db.rollback()
        print(f"[EMAIL] Failed to queue verification email: {e}")


def create_user(db: Session, user: UserCreate, is_admin: bool = False):
    # Check if this is the first user - if so, make them admin
    user_count = db.query(User).count()
    if user_count == 0:
        is_admin = True

    # Handle referral code if provided
    referrer = None
    if user.referral_code:
        referrer = db.query(User).filter(User.referral_code == user.referral_code).first()
        # Don't raise error for invalid referral codes - just ignore them for security

    hashed_password = get_password_hash(user.password)
    settings = get_settings()

    # Create user with initial balance of 0 - credits will be added separately
    db_user = User(
        email=user.email,
        hashed_password=hashed_password,
        username=user.username,
        is_admin=is_admin,
        balance=Decimal("0"),  # Start with 0, add credits separately
        is_verified=(not settings.USE_EMAIL),
        referred_by_id=referrer.id if referrer else None,
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # Create signup credit if configured
    signup_credit_service = SignupCreditService(db)
    signup_credit = signup_credit_service.create_signup_credit(db_user)

    # Update user balance with signup credit amount
    if signup_credit:
        db_user.balance = signup_credit.amount
        db.add(db_user)
        db.commit()
        db.refresh(db_user)

    # Handle referral credits if user was referred
    if referrer:
        referral_service = ReferralService(db)
        referral_service.create_referral_credits(referrer, db_user)

    # Send verification email if enabled
    _maybe_send_verification(db, db_user)

    return db_user


def check_user_self_deletion_preconditions(db: Session, user_id: int):
    """Check if a user can delete their own account (precondition validation)"""
    from app.models.deployment import Deployment

    db_user = db.query(User).filter(User.id == user_id).first()
    if not db_user:
        return None

    # Check for active deployments (only requirement for self-deletion)
    active_deployments = db.query(Deployment).filter(
        Deployment.user_id == user_id,
        Deployment.status.in_(['CREATING', 'PROVISIONING', 'ACTIVE'])
    ).all()

    # For self-deletion, we only check deployments (not balance)
    can_delete = len(active_deployments) == 0

    return {
        'user': db_user,
        'can_delete': can_delete,
        'active_deployments': active_deployments,
        'active_deployment_count': len(active_deployments),
        'issues': {
            'has_active_deployments': len(active_deployments) > 0
        }
    }


def self_delete_user(db: Session, user_id: int):
    """
    User self-deletion - deactivates account instead of deleting for audit trail.
    Only checks for active deployments, not balance or transactions.
    """
    from app.models.deployment import Deployment

    # First check preconditions
    precondition_check = check_user_self_deletion_preconditions(db, user_id)
    if not precondition_check:
        return None

    if not precondition_check['can_delete']:
        # Return the precondition check result with error info
        return {
            'success': False,
            'error': 'Active deployments found',
            'precondition_check': precondition_check
        }

    db_user = precondition_check['user']

    # Store original info for audit trail
    original_email = db_user.email
    original_username = db_user.username

    # Mark as deactivated by changing username only (keep email for identification)
    # This preserves the account for audit trail instead of deleting
    db_user.username = f"deactivated_{user_id}"
    db_user.is_admin = False
    # Note: We preserve balance and transactions for audit trail

    db.add(db_user)
    db.commit()

    return {
        'success': True,
        'user': db_user,
        'original_email': original_email,
        'original_username': original_username,
        'action': 'deactivated'  # Indicates account was deactivated, not deleted
    }





def get_admin_users(db: Session):
    return db.query(User).filter(User.is_admin == True).all()


def make_user_admin(db: Session, user_id: int):
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user:
        db_user.is_admin = True
        db.commit()
        db.refresh(db_user)
    return db_user


def remove_admin_role(db: Session, user_id: int):
    # Check if this is the last admin
    admin_count = db.query(User).filter(User.is_admin == True).count()
    if admin_count <= 1:
        return None  # Cannot remove the last admin

    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user and db_user.is_admin:
        db_user.is_admin = False
        db.commit()
        db.refresh(db_user)
    return db_user


def prepare_user_for_deletion(db: Session, user_id: int):
    """Prepare a user for deletion by deactivating deployments and zeroing balance"""
    from app.models.deployment import Deployment

    db_user = db.query(User).filter(User.id == user_id).first()
    if not db_user:
        return None

    # Get user's active deployments
    active_deployments = db.query(Deployment).filter(
        Deployment.user_id == user_id,
        Deployment.status.in_(['CREATING', 'PROVISIONING', 'ACTIVE'])
    ).all()

    # Mark all active deployments as terminated
    for deployment in active_deployments:
        deployment.status = 'TERMINATED'
        db.add(deployment)

    # Zero out the user's balance (set to -0.01 to indicate prepared for deletion)
    db_user.balance = -0.01
    db.add(db_user)

    db.commit()

    return {
        'user': db_user,
        'deactivated_deployments': len(active_deployments),
        'deployment_names': [dep.name for dep in active_deployments]
    }


def get_user_deployments(db: Session, user_id: int):
    """Get all deployments for a specific user"""
    from app.models.deployment import Deployment

    return db.query(Deployment).filter(Deployment.user_id == user_id).all()


def check_user_deactivation_preconditions(db: Session, user_id: int):
    """Check if a user can be deactivated (precondition validation)"""
    from app.models.deployment import Deployment

    db_user = db.query(User).filter(User.id == user_id).first()
    if not db_user:
        return None

    # Check for active deployments
    active_deployments = db.query(Deployment).filter(
        Deployment.user_id == user_id,
        Deployment.status.in_(['CREATING', 'PROVISIONING', 'ACTIVE'])
    ).all()

    # Check balance
    user_balance = float(db_user.balance or 0)

    # Determine if deactivation is allowed
    can_deactivate = len(active_deployments) == 0 and user_balance <= 0

    return {
        'user': db_user,
        'can_deactivate': can_deactivate,
        'active_deployments': active_deployments,
        'active_deployment_count': len(active_deployments),
        'balance': user_balance,
        'issues': {
            'has_active_deployments': len(active_deployments) > 0,
            'has_positive_balance': user_balance > 0
        }
    }


def deactivate_user(db: Session, user_id: int):
    """Deactivate a user account - only if preconditions are met"""
    from app.models.deployment import Deployment

    # First check preconditions
    precondition_check = check_user_deactivation_preconditions(db, user_id)
    if not precondition_check:
        return None

    if not precondition_check['can_deactivate']:
        # Return the precondition check result with error info
        return {
            'success': False,
            'error': 'Preconditions not met',
            'precondition_check': precondition_check
        }

    db_user = precondition_check['user']

    # Store original info for audit trail
    original_email = db_user.email
    original_username = db_user.username

    # Mark as deactivated by changing username only (keep email for identification)
    db_user.username = f"deactivated_{user_id}"
    db_user.is_admin = False
    # Note: We don't change balance here - admin must handle that manually

    db.add(db_user)
    db.commit()

    return {
        'success': True,
        'user': db_user,
        'original_email': original_email,
        'original_username': original_username
    }
