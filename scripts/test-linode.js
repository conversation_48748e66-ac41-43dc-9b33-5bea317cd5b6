const axios = require('axios');
require('dotenv').config();

const LINODE_TOKEN = process.env.LINODE_TOKEN;

async function testLinodeAPI() {
    console.log('🔍 Testing Linode API...');
    console.log('Token configured:', !!LINODE_TOKEN);
    
    if (!LINODE_TOKEN) {
        console.error('❌ LINODE_TOKEN not configured');
        return;
    }

    try {
        // Test 1: List instances
        console.log('\n📋 Testing: List instances');
        const instancesRes = await axios.get('https://api.linode.com/v4/linode/instances', {
            headers: { Authorization: `Bearer ${LINODE_TOKEN}` }
        });
        
        console.log(`✅ Found ${instancesRes.data.data.length} instances`);
        
        if (instancesRes.data.data.length === 0) {
            console.log('⚠️  No instances found');
            return;
        }

        const instance = instancesRes.data.data[0];
        console.log(`📊 Testing with instance: ${instance.label} (ID: ${instance.id})`);

        // Test 2: Get instance details
        console.log('\n📋 Testing: Get instance details');
        const detailsRes = await axios.get(`https://api.linode.com/v4/linode/instances/${instance.id}`, {
            headers: { Authorization: `Bearer ${LINODE_TOKEN}` }
        });
        console.log(`✅ Instance type: ${detailsRes.data.type}`);
        console.log(`✅ Instance status: ${detailsRes.data.status}`);

        // Test 3: Get plan details
        console.log('\n📋 Testing: Get plan details');
        const planRes = await axios.get(`https://api.linode.com/v4/linode/types/${detailsRes.data.type}`, {
            headers: { Authorization: `Bearer ${LINODE_TOKEN}` }
        });
        console.log(`✅ Plan transfer limit: ${planRes.data.transfer} GB`);

        // Test 4: Get stats (this is likely where the error occurs)
        console.log('\n📊 Testing: Get bandwidth stats');
        try {
            const statsRes = await axios.get(`https://api.linode.com/v4/linode/instances/${instance.id}/stats`, {
                headers: { Authorization: `Bearer ${LINODE_TOKEN}` }
            });
            console.log('✅ Stats retrieved successfully');
            console.log('📊 Stats structure:', Object.keys(statsRes.data));
            
            if (statsRes.data.data && statsRes.data.data.network) {
                console.log('📊 Network stats available:', Object.keys(statsRes.data.data.network));
                console.log('📊 Network out data points:', statsRes.data.data.network.out?.length || 0);
            }
        } catch (statsError) {
            console.error('❌ Stats API Error:', statsError.response?.status, statsError.response?.statusText);
            console.error('❌ Error details:', statsError.response?.data);
            
            // Try alternative stats endpoint
            console.log('\n🔄 Trying alternative stats endpoint...');
            try {
                const altStatsRes = await axios.get(`https://api.linode.com/v4/linode/instances/${instance.id}/stats/2024/1`, {
                    headers: { Authorization: `Bearer ${LINODE_TOKEN}` }
                });
                console.log('✅ Alternative stats endpoint works');
            } catch (altError) {
                console.error('❌ Alternative stats also failed:', altError.response?.status);
            }
        }

    } catch (error) {
        console.error('❌ API Test failed:', error.response?.status, error.response?.statusText);
        console.error('❌ Error details:', error.response?.data);
    }
}

testLinodeAPI();
