const { LightsailClient, GetInstancesCommand, StopInstanceCommand } = require("@aws-sdk/client-lightsail");
const { CloudWatchClient, GetMetricDataCommand, GetMetricStatisticsCommand } = require("@aws-sdk/client-cloudwatch");

class AWSLightsailProvider {
    constructor(accessKeyId, secretAccessKey, region) {
        this.accessKeyId = accessKeyId;
        this.secretAccessKey = secretAccessKey;
        this.region = region;
        this.name = "Lightsail";

        const credentials = {
            accessKeyId: this.accessKeyId,
            secretAccessKey: this.secretAccessKey
        };

        this.lightsailClient = this.accessKeyId ? new LightsailClient({
            region: this.region,
            credentials
        }) : null;

        this.cloudwatchClient = this.accessKeyId ? new CloudWatchClient({
            region: this.region,
            credentials
        }) : null;
    }

    async getBandwidthUsage(instanceId, instanceRegion = null) {
        if (!this.accessKeyId || !this.secretAccessKey) {
            console.log("Lightsail: No credentials configured, skipping");
            return null;
        }

        // If we have the region from listServers, use it; otherwise try to find the instance
        let targetRegion = instanceRegion || this.region;
        let instance = null;

        try {
            // Create clients for the target region
            const regionalLightsailClient = new LightsailClient({
                region: targetRegion,
                credentials: {
                    accessKeyId: this.accessKeyId,
                    secretAccessKey: this.secretAccessKey
                }
            });

            const regionalCloudwatchClient = new CloudWatchClient({
                region: targetRegion,
                credentials: {
                    accessKeyId: this.accessKeyId,
                    secretAccessKey: this.secretAccessKey
                }
            });

            // Get instance details to determine the plan
            const instanceCommand = new GetInstancesCommand({ instanceName: instanceId });
            const instanceData = await regionalLightsailClient.send(instanceCommand);

            if (!instanceData.instances || instanceData.instances.length === 0) {
                console.error(`Lightsail: Instance ${instanceId} not found in region ${targetRegion}`);
                return null;
            }

            instance = instanceData.instances[0];
            console.log(`Lightsail: Found instance ${instanceId} in region ${targetRegion}`);

            // Lightsail transfer allowances by bundle (in GB)
            const transferAllowanceGB = this.getTransferAllowance(instance.bundleId);
            const includedBytes = transferAllowanceGB * (1024 ** 3);

            // Get bandwidth metrics from CloudWatch for the last 24 hours
            const now = new Date();
            const oneDayAgo = new Date(now.getTime() - (24 * 60 * 60 * 1000));

            let totalOutgoingBytes = 0;

            try {
                // Try GetMetricStatistics first (more likely to be permitted)
                const statsCommand = new GetMetricStatisticsCommand({
                    Namespace: 'AWS/Lightsail',
                    MetricName: 'NetworkOut',
                    Dimensions: [
                        {
                            Name: 'InstanceName',
                            Value: instanceId
                        }
                    ],
                    StartTime: oneDayAgo,
                    EndTime: now,
                    Period: 3600, // 1 hour periods
                    Statistics: ['Sum']
                });

                const statsData = await regionalCloudwatchClient.send(statsCommand);

                if (statsData.Datapoints && statsData.Datapoints.length > 0) {
                    // Sum all the datapoints (each datapoint is bytes for that hour)
                    totalOutgoingBytes = statsData.Datapoints.reduce((sum, datapoint) => sum + (datapoint.Sum || 0), 0);
                    console.log(`Lightsail: Got bandwidth data for instance ${instanceId}: ${(totalOutgoingBytes / (1024**3)).toFixed(3)} GB`);
                    console.log(`Lightsail: Found ${statsData.Datapoints.length} datapoints for the last 24 hours`);
                } else {
                    console.log(`Lightsail: No bandwidth data available for instance ${instanceId} yet (normal for new instances)`);
                }

            } catch (metricsError) {
                if (metricsError.message.includes('not authorized')) {
                    if (metricsError.message.includes('cloudwatch:GetMetricStatistics')) {
                        console.log(`Lightsail: CloudWatch GetMetricStatistics permission denied for ${instanceId}.`);
                        console.log(`💡 Add cloudwatch:GetMetricStatistics permission to your IAM user for bandwidth monitoring.`);
                    } else {
                        console.log(`Lightsail: CloudWatch permissions issue for ${instanceId}:`, metricsError.message);
                    }
                } else {
                    console.log(`Lightsail: CloudWatch metrics error for ${instanceId}:`, metricsError.message);
                }
                // For new instances or when metrics are unavailable, return 0 usage
                totalOutgoingBytes = 0;
            }

            return {
                outgoingBytes: totalOutgoingBytes,
                includedBytes: includedBytes
            };

        } catch (error) {
            console.error(`Lightsail: Failed to get bandwidth for instance ${instanceId}:`, error.message);
            return null;
        }
    }

    // Helper method to get transfer allowance based on bundle ID
    getTransferAllowance(bundleId) {
        // Lightsail transfer allowances (in GB) by bundle
        const transferAllowances = {
            'nano_3_0': 1000,      // 1TB
            'micro_3_0': 2000,     // 2TB
            'small_3_0': 3000,     // 3TB
            'medium_3_0': 4000,    // 4TB
            'large_3_0': 5000,     // 5TB
            'xlarge_3_0': 6000,    // 6TB
            '2xlarge_3_0': 7000,   // 7TB
            // Add more bundles as needed
        };

        return transferAllowances[bundleId] || 1000; // Default to 1TB if unknown
    }

    async stopInstance(instanceId) {
        if (!this.lightsailClient) {
            console.log("Lightsail: No credentials configured, skipping");
            return false;
        }
        try {
            const command = new StopInstanceCommand({ instanceName: instanceId });
            await this.lightsailClient.send(command);
            console.log(`Lightsail: Instance ${instanceId} stopped.`);
            return true;
        } catch (error) {
            console.error(`Lightsail: Failed to stop instance ${instanceId}:`, error.message);
            return false;
        }
    }

    async listServers() {
        if (!this.lightsailClient) {
            console.log("Lightsail: No credentials configured, skipping");
            return [];
        }

        // Lightsail supported regions (as of 2024)
        const lightsailRegions = [
            'us-east-1', 'us-east-2', 'us-west-2',
            'ca-central-1',
            'eu-west-1', 'eu-west-2', 'eu-west-3', 'eu-central-1', 'eu-north-1',
            'ap-south-1', 'ap-southeast-1', 'ap-southeast-2', 'ap-northeast-1', 'ap-northeast-2'
        ];

        const allInstances = [];

        for (const region of lightsailRegions) {
            try {
                // Create a client for this specific region
                const regionalClient = new LightsailClient({
                    region: region,
                    credentials: {
                        accessKeyId: this.accessKeyId,
                        secretAccessKey: this.secretAccessKey
                    }
                });

                const command = new GetInstancesCommand({});
                const data = await regionalClient.send(command);

                if (data.instances && data.instances.length > 0) {
                    console.log(`Lightsail: Found ${data.instances.length} instance(s) in ${region}`);

                    const regionInstances = data.instances.map(instance => ({
                        id: instance.name, // Lightsail uses instance name as identifier
                        name: instance.name,
                        provider: this.name,
                        bundleId: instance.bundleId, // Store bundle ID for transfer limits
                        state: instance.state?.name || 'unknown',
                        region: region // Store region for bandwidth monitoring
                    }));

                    allInstances.push(...regionInstances);
                }
            } catch (error) {
                // Only log errors that aren't just "no instances" - some regions might not be accessible
                if (!error.message.includes('UnauthorizedOperation') && !error.message.includes('InvalidUserID')) {
                    console.log(`Lightsail: Error checking region ${region}:`, error.message);
                }
            }
        }

        if (allInstances.length === 0) {
            console.log("Lightsail: No instances found in any region");
        } else {
            console.log(`Lightsail: Total instances found across all regions: ${allInstances.length}`);
        }

        return allInstances;
    }
}

module.exports = AWSLightsailProvider;
