# =============================================================================
# MULTI-CLOUD BANDWIDTH MONITORING CONFIGURATION
# =============================================================================
# Copy this file to .env and configure your API credentials and thresholds

# =============================================================================
# CLOUD PROVIDER API CREDENTIALS
# =============================================================================
# Configure only the providers you want to monitor
# Leave blank or comment out providers you don't use

# Hetzner Cloud
HCLOUD_TOKEN=your_hetzner_api_token_here

# Vultr
VULTR_API_KEY=your_vultr_api_key_here

# Linode
LINODE_TOKEN=your_linode_api_token_here

# DigitalOcean
DIGITALOCEAN_TOKEN=your_digitalocean_api_token_here

# AWS Lightsail
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1

# Microsoft Azure
AZURE_CLIENT_ID=your_azure_client_id
AZURE_CLIENT_SECRET=your_azure_client_secret
AZURE_TENANT_ID=your_azure_tenant_id
AZURE_SUBSCRIPTION_ID=your_azure_subscription_id

# Google Cloud Platform
GCP_PROJECT_ID=your_gcp_project_id
GCP_CREDENTIALS_FILE=/path/to/your/service-account-key.json

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================
# Configure Discord bot for alerts (optional)

# Discord Bot Token (get from Discord Developer Portal)
DISCORD_BOT_TOKEN=your_discord_bot_token_here

# Discord Channel ID (where to send notifications)
DISCORD_CHANNEL_ID=your_discord_channel_id_here

# =============================================================================
# LEGACY MONITORING THRESHOLDS (deprecated)
# =============================================================================
# These are kept for backward compatibility but are overridden by advanced settings

NOTIFICATION_THRESHOLD_PERCENT=80
KILL_THRESHOLD_PERCENT=95
SEND_USAGE_NOTIF_ALWAYS=false
OBFUSCATE_SERVER_NAMES=false

# =============================================================================
# ADVANCED BANDWIDTH MONITORING
# =============================================================================
# Sophisticated multi-level bandwidth monitoring with burst detection

# Monthly bandwidth limit in GB (20TB = 20480GB)
# This is your absolute monthly limit - exceeding this triggers immediate termination
MONTHLY_BANDWIDTH_LIMIT_GB=20480

# Daily burst allowance in GB
# Allows temporary spikes above normal usage
# Production: 1000 (1TB), Testing: 10
DAILY_BURST_LIMIT_GB=1000

# Burst window in hours
# How long burst usage is allowed before triggering termination
# Production: 24, Testing: 6
BURST_WINDOW_HOURS=24

# Early warning threshold in GB
# Sends alerts when daily usage exceeds this amount
# Production: 500, Testing: 5
EARLY_WARNING_THRESHOLD_GB=500

# Enable actual instance termination
# IMPORTANT: Set to false for testing, true for production
# When false: Shows what would happen without actually terminating
# When true: Actually terminates instances that exceed limits
ENABLE_INSTANCE_TERMINATION=false

# =============================================================================
# TESTING CONFIGURATION EXAMPLES
# =============================================================================
# For testing the system with lower thresholds:
#
# MONTHLY_BANDWIDTH_LIMIT_GB=20480
# DAILY_BURST_LIMIT_GB=10           # Low for testing
# BURST_WINDOW_HOURS=6              # Short window for testing
# EARLY_WARNING_THRESHOLD_GB=5      # Low for testing
# ENABLE_INSTANCE_TERMINATION=false # Safe mode for testing
#
# For production use:
#
# MONTHLY_BANDWIDTH_LIMIT_GB=20480
# DAILY_BURST_LIMIT_GB=1000         # 1TB daily burst allowance
# BURST_WINDOW_HOURS=24             # 24 hour burst window
# EARLY_WARNING_THRESHOLD_GB=500    # 500GB early warning
# ENABLE_INSTANCE_TERMINATION=true  # Enable actual termination
