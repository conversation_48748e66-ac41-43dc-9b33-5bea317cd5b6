const axios = require('axios');
require('dotenv').config();

// Import all providers
const HetznerProvider = require('./providers/hetzner');
const VultrProvider = require('./providers/vultr');
const LinodeProvider = require('./providers/linode');
const DigitalOceanProvider = require('./providers/digitalocean');
const AWSLightsailProvider = require('./providers/aws-lightsail');
const AzureProvider = require('./providers/azure');
const GCPProvider = require('./providers/gcp');

async function testAllProviders() {
    console.log('🔍 Testing All Cloud Providers...\n');

    const providers = [
        {
            name: 'Hetz<PERSON>',
            provider: new HetznerProvider(process.env.HCLOUD_TOKEN),
            configured: !!process.env.HCLOUD_TOKEN
        },
        {
            name: 'Vultr',
            provider: new VultrProvider(process.env.VULTR_API_KEY),
            configured: !!process.env.VULTR_API_KEY
        },
        {
            name: 'Linode',
            provider: new <PERSON>odeProvider(process.env.LINODE_TOKEN),
            configured: !!process.env.LINODE_TOKEN
        },
        {
            name: 'DigitalOcean',
            provider: new DigitalOceanProvider(process.env.DIGITALOCEAN_TOKEN),
            configured: !!process.env.DIGITALOCEAN_TOKEN
        },
        {
            name: 'AWS Lightsail',
            provider: new AWSLightsailProvider(
                process.env.AWS_ACCESS_KEY_ID,
                process.env.AWS_SECRET_ACCESS_KEY,
                process.env.AWS_REGION || 'us-east-1'
            ),
            configured: !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY)
        },
        {
            name: 'Azure',
            provider: new AzureProvider(
                process.env.AZURE_CLIENT_ID,
                process.env.AZURE_CLIENT_SECRET,
                process.env.AZURE_TENANT_ID,
                process.env.AZURE_SUBSCRIPTION_ID
            ),
            configured: !!(process.env.AZURE_CLIENT_ID && process.env.AZURE_CLIENT_SECRET)
        },
        {
            name: 'GCP',
            provider: new GCPProvider(
                process.env.GCP_PROJECT_ID,
                process.env.GCP_CREDENTIALS_FILE
            ),
            configured: !!(process.env.GCP_PROJECT_ID && process.env.GCP_CREDENTIALS_FILE)
        }
    ];

    for (const { name, provider, configured } of providers) {
        console.log(`\n${'='.repeat(50)}`);
        console.log(`🔧 Testing ${name}`);
        console.log(`${'='.repeat(50)}`);
        
        if (!configured) {
            console.log(`❌ ${name}: Not configured (missing credentials)`);
            continue;
        }

        try {
            // Test 1: List servers
            console.log(`📋 ${name}: Listing servers...`);
            const servers = await provider.listServers();
            
            if (servers.length === 0) {
                console.log(`⚠️  ${name}: No servers found`);
                continue;
            }

            console.log(`✅ ${name}: Found ${servers.length} server(s)`);
            
            // Test each server
            for (const server of servers) {
                console.log(`\n📊 Testing server: ${server.name} (ID: ${server.id})`);
                
                // Test 2: Get bandwidth usage
                try {
                    console.log(`📈 ${name}: Getting bandwidth usage...`);
                    let usage;
                    
                    // Handle special cases for providers that need extra parameters
                    if (name === 'GCP' && server.zone && server.numericId) {
                        usage = await provider.getBandwidthUsage(server.id, server.zone, server.numericId);
                    } else if (name === 'AWS Lightsail' && server.region) {
                        usage = await provider.getBandwidthUsage(server.id, server.region);
                    } else {
                        usage = await provider.getBandwidthUsage(server.id);
                    }
                    
                    if (usage) {
                        const usageGB = usage.outgoingBytes / (1024 ** 3);
                        const limitGB = usage.includedBytes / (1024 ** 3);
                        console.log(`✅ ${name}: Bandwidth usage: ${usageGB.toFixed(3)} GB / ${limitGB.toFixed(0)} GB`);
                        
                        if (usageGB === 0) {
                            console.log(`ℹ️  ${name}: Zero usage is normal for new instances`);
                        }
                    } else {
                        console.log(`❌ ${name}: Failed to get bandwidth usage`);
                    }
                } catch (bandwidthError) {
                    console.log(`❌ ${name}: Bandwidth error:`, bandwidthError.message);
                }
            }
            
        } catch (error) {
            console.log(`❌ ${name}: Provider error:`, error.message);
        }
    }

    console.log(`\n${'='.repeat(50)}`);
    console.log('🎯 Test Summary');
    console.log(`${'='.repeat(50)}`);
    
    // Generate traffic test suggestions
    console.log('\n💡 To generate test traffic on your VPS instances:');
    console.log('');
    console.log('1. SSH into each VPS and run:');
    console.log('   curl -o /dev/null http://speedtest.tele2.net/100MB.zip');
    console.log('   curl -o /dev/null http://speedtest.tele2.net/1GB.zip');
    console.log('');
    console.log('2. Wait 5-10 minutes for metrics to update');
    console.log('');
    console.log('3. Run the monitor again:');
    console.log('   node monitor.js');
    console.log('');
    console.log('4. Check for non-zero bandwidth usage');
}

// Run the test
testAllProviders().catch(console.error);
