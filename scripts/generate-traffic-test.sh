#!/bin/bash

# Traffic Generation Test Script for Bandwidth Monitoring
# This script helps you generate network traffic to test the bandwidth monitoring system

echo "🚀 Bandwidth Monitoring Traffic Generation Test"
echo "=============================================="
echo ""

# Function to generate traffic
generate_traffic() {
    local size=$1
    local description=$2
    
    echo "📊 Generating $description traffic..."
    echo "Command: curl -o /dev/null http://speedtest.tele2.net/${size}.zip"
    echo ""
    echo "Run this command on each of your VPS instances:"
    echo "----------------------------------------"
    echo "curl -o /dev/null http://speedtest.tele2.net/${size}.zip"
    echo "----------------------------------------"
    echo ""
}

# Function to show monitoring commands
show_monitoring_commands() {
    echo "🔍 Monitoring Commands"
    echo "====================="
    echo ""
    echo "1. Test all providers:"
    echo "   node test-all-providers.js"
    echo ""
    echo "2. Run bandwidth monitoring:"
    echo "   node monitor.js"
    echo ""
    echo "3. Test specific provider:"
    echo "   node test-linode.js"
    echo "   node test-digitalocean.js"
    echo "   node test-lightsail.js"
    echo ""
}

# Function to show SSH commands for each provider
show_ssh_commands() {
    echo "🔑 SSH into your VPS instances and run traffic generation:"
    echo "========================================================"
    echo ""
    echo "For each of your VPS instances, SSH in and run:"
    echo ""
    echo "# Small test (100MB):"
    echo "curl -o /dev/null http://speedtest.tele2.net/100MB.zip"
    echo ""
    echo "# Medium test (1GB):"
    echo "curl -o /dev/null http://speedtest.tele2.net/1GB.zip"
    echo ""
    echo "# Large test (10GB) - BE CAREFUL with this one:"
    echo "curl -o /dev/null http://speedtest.tele2.net/10GB.zip"
    echo ""
    echo "⚠️  WARNING: The 10GB test will use significant bandwidth!"
    echo "   Only use it if you want to test threshold triggers."
    echo ""
}

# Function to show expected timeline
show_timeline() {
    echo "⏰ Expected Timeline for Bandwidth Monitoring"
    echo "============================================"
    echo ""
    echo "1. Generate traffic (1-2 minutes)"
    echo "   └── Run curl commands on your VPS instances"
    echo ""
    echo "2. Wait for metrics to update (5-15 minutes)"
    echo "   ├── Hetzner: ~5 minutes"
    echo "   ├── Vultr: ~5-10 minutes"
    echo "   ├── Linode: ~5-10 minutes"
    echo "   ├── DigitalOcean: ~10-15 minutes"
    echo "   ├── AWS Lightsail: ~5-15 minutes"
    echo "   └── Azure: ~10-15 minutes"
    echo ""
    echo "3. Run monitoring script"
    echo "   └── node monitor.js"
    echo ""
    echo "4. Check for non-zero bandwidth usage"
    echo "   └── Should see usage > 0.000 GB"
    echo ""
}

# Function to show current thresholds
show_current_thresholds() {
    echo "🎯 Current Monitoring Thresholds"
    echo "==============================="
    echo ""
    if [ -f ".env" ]; then
        echo "From your .env file:"
        echo ""
        grep -E "(DAILY_BURST_LIMIT_GB|EARLY_WARNING_THRESHOLD_GB|MONTHLY_BANDWIDTH_LIMIT_GB|ENABLE_INSTANCE_TERMINATION)" .env | while read line; do
            echo "  $line"
        done
        echo ""
        echo "💡 With current settings:"
        echo "  - Early warning at: $(grep EARLY_WARNING_THRESHOLD_GB .env | cut -d'=' -f2 | cut -d'#' -f1 | tr -d ' ') GB"
        echo "  - Burst limit at: $(grep DAILY_BURST_LIMIT_GB .env | cut -d'=' -f2 | cut -d'#' -f1 | tr -d ' ') GB"
        echo "  - Termination: $(grep ENABLE_INSTANCE_TERMINATION .env | cut -d'=' -f2 | cut -d'#' -f1 | tr -d ' ')"
        echo ""
    else
        echo "❌ .env file not found"
    fi
}

# Function to show test scenarios
show_test_scenarios() {
    echo "🧪 Test Scenarios"
    echo "================"
    echo ""
    echo "Scenario 1: Basic Functionality Test"
    echo "  1. Download 100MB file on each VPS"
    echo "  2. Wait 10 minutes"
    echo "  3. Run: node monitor.js"
    echo "  4. Expect: Non-zero usage, no alerts"
    echo ""
    echo "Scenario 2: Early Warning Test"
    echo "  1. Download files totaling > 2GB on one VPS"
    echo "  2. Wait 10 minutes"
    echo "  3. Run: node monitor.js"
    echo "  4. Expect: Early warning alert"
    echo ""
    echo "Scenario 3: Burst Limit Test"
    echo "  1. Download files totaling > 2GB on one VPS"
    echo "  2. Wait 10 minutes"
    echo "  3. Run: node monitor.js"
    echo "  4. Expect: Burst limit warning"
    echo ""
    echo "Scenario 4: Discord Notification Test"
    echo "  1. Trigger any alert scenario above"
    echo "  2. Check your Discord channel for notifications"
    echo "  3. Verify rich embed formatting"
    echo ""
}

# Main menu
while true; do
    echo ""
    echo "Choose an option:"
    echo "1. Show traffic generation commands"
    echo "2. Show monitoring commands"
    echo "3. Show SSH commands for VPS instances"
    echo "4. Show expected timeline"
    echo "5. Show current thresholds"
    echo "6. Show test scenarios"
    echo "7. Run monitoring now"
    echo "8. Test all providers now"
    echo "9. Exit"
    echo ""
    read -p "Enter your choice (1-9): " choice
    
    case $choice in
        1)
            generate_traffic "100MB" "small (100MB)"
            generate_traffic "1GB" "medium (1GB)"
            generate_traffic "10GB" "large (10GB) - CAUTION"
            ;;
        2)
            show_monitoring_commands
            ;;
        3)
            show_ssh_commands
            ;;
        4)
            show_timeline
            ;;
        5)
            show_current_thresholds
            ;;
        6)
            show_test_scenarios
            ;;
        7)
            echo "🔍 Running bandwidth monitoring..."
            node monitor.js
            ;;
        8)
            echo "🔍 Testing all providers..."
            node test-all-providers.js
            ;;
        9)
            echo "👋 Goodbye!"
            exit 0
            ;;
        *)
            echo "❌ Invalid choice. Please enter 1-9."
            ;;
    esac
done
