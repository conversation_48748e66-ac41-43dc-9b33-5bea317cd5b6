/**
 * Convert bytes to terabytes with specified precision
 * @param {number} bytes - Number of bytes
 * @param {number} precision - Number of decimal places (default: 4)
 * @returns {string} - Formatted terabytes string
 */
function bytesToTB(bytes, precision = 4) {
    return (bytes / (1024 ** 4)).toFixed(precision);
}

/**
 * Calculate percentage of used vs total
 * @param {number} used - Used amount
 * @param {number} total - Total amount
 * @returns {string} - Percentage string with % symbol
 */
function calculatePercentage(used, total) {
    if (!total) return "0.00%";
    return ((used / total) * 100).toFixed(2) + "%";
}

/**
 * Obfuscate server names for privacy
 * @param {string} name - Original server name
 * @param {boolean} shouldObfuscate - Whether to obfuscate or not
 * @returns {string} - Obfuscated or original name
 */
function obfuscateName(name, shouldObfuscate = false) {
    if (!shouldObfuscate || !name) return name;
    if (name.length <= 2) return name;
    const firstChar = name.charAt(0);
    const lastChar = name.charAt(name.length - 1);
    const middleLength = name.length - 2;
    return `${firstChar}${"X".repeat(middleLength)}${lastChar}`;
}

/**
 * Process server data and determine if it needs notifications or shutdown
 * @param {Object} server - Server object with id, name, provider
 * @param {Object} usage - Usage object with outgoingBytes, includedBytes
 * @param {number} notificationThreshold - Percentage threshold for notifications
 * @param {number} killThreshold - Percentage threshold for shutdown
 * @param {boolean} shouldObfuscate - Whether to obfuscate server names
 * @returns {Object} - Processed server data with usage info
 */
function processServerData(server, usage, notificationThreshold, killThreshold, shouldObfuscate) {
    const outgoingTB = bytesToTB(usage.outgoingBytes);
    const limitTB = bytesToTB(usage.includedBytes);
    const usagePercentage = calculatePercentage(usage.outgoingBytes, usage.includedBytes);
    const rawPercentage = usage.includedBytes ? (usage.outgoingBytes / usage.includedBytes) * 100 : 0;

    const serverData = {
        id: server.id,
        name: obfuscateName(server.name, shouldObfuscate),
        provider: server.provider,
        outgoingTB,
        limitTB,
        usagePercentage,
        rawPercentage,
        zone: server.zone, // For GCP instances
    };

    // Determine action needed
    if (rawPercentage >= killThreshold) {
        serverData.action = 'kill';
    } else if (rawPercentage >= notificationThreshold) {
        serverData.action = 'notify';
    } else {
        serverData.action = 'none';
    }

    return serverData;
}

module.exports = {
    bytesToTB,
    calculatePercentage,
    obfuscateName,
    processServerData
};
