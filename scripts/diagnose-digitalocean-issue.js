const axios = require('axios');
require('dotenv').config();

async function diagnoseDigitalOceanIssue() {
    console.log('🔍 Diagnosing DigitalOcean Traffic Detection Issue');
    console.log('=================================================');
    console.log('');
    console.log('You\'ve downloaded 3GB total (3 × 1GB) but DigitalOcean shows only 28KB.');
    console.log('Let\'s investigate why...');
    console.log('');

    const DIGITALOCEAN_TOKEN = process.env.DIGITALOCEAN_TOKEN;
    
    if (!DIGITALOCEAN_TOKEN) {
        console.error('❌ DIGITALOCEAN_TOKEN not configured');
        return;
    }

    try {
        // Get droplet info
        const dropletsRes = await axios.get('https://api.digitalocean.com/v2/droplets', {
            headers: { Authorization: `Bearer ${DIGITALOCEAN_TOKEN}` }
        });
        
        const droplet = dropletsRes.data.droplets[0];
        console.log(`📊 Droplet: ${droplet.name} (ID: ${droplet.id})`);
        console.log(`📊 Status: ${droplet.status}`);
        console.log(`📊 Created: ${droplet.created_at}`);
        console.log(`📊 Region: ${droplet.region.name}`);
        console.log(`📊 Size: ${droplet.size.slug}`);
        console.log(`📊 Image: ${droplet.image.name}`);
        console.log('');

        // Check if monitoring is enabled
        console.log('🔍 Checking monitoring capabilities...');
        console.log('');

        // Test 1: Check if droplet supports monitoring
        if (droplet.features) {
            console.log(`📊 Droplet features: ${droplet.features.join(', ')}`);
            if (droplet.features.includes('monitoring')) {
                console.log('✅ Monitoring feature is enabled');
            } else {
                console.log('❌ Monitoring feature is NOT enabled - this could be the issue!');
            }
        }

        // Test 2: Check available metrics
        console.log('');
        console.log('🔍 Testing different metric endpoints...');
        
        const now = Math.floor(Date.now() / 1000);
        const start = now - (24 * 3600);

        // Test different interfaces and directions
        const testCases = [
            { interface: 'public', direction: 'inbound' },
            { interface: 'public', direction: 'outbound' },
            { interface: 'private', direction: 'inbound' },
            { interface: 'private', direction: 'outbound' }
        ];

        for (const testCase of testCases) {
            try {
                console.log(`\n📊 Testing ${testCase.interface} ${testCase.direction}...`);
                
                const res = await axios.get(
                    `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=${testCase.interface}&direction=${testCase.direction}&start=${start}&end=${now}`,
                    { headers: { Authorization: `Bearer ${DIGITALOCEAN_TOKEN}` } }
                );
                
                console.log(`   ✅ Endpoint accessible`);
                console.log(`   📊 Response status: ${res.data.status}`);
                
                if (res.data.data && res.data.data.result) {
                    console.log(`   📊 Results: ${res.data.data.result.length}`);
                    
                    if (res.data.data.result.length > 0) {
                        const result = res.data.data.result[0];
                        if (result.values) {
                            console.log(`   📊 Data points: ${result.values.length}`);
                            
                            let total = 0;
                            for (const [timestamp, value] of result.values) {
                                total += parseFloat(value) * 300;
                            }
                            console.log(`   📊 Total: ${total} bytes (${(total / (1024**3)).toFixed(6)} GB)`);
                            
                            if (total > 1024 * 1024) { // More than 1MB
                                console.log(`   🎉 Found significant traffic!`);
                            }
                        }
                    }
                } else {
                    console.log(`   ⚠️  No data in response`);
                }
            } catch (error) {
                console.log(`   ❌ Error: ${error.response?.status} - ${error.message}`);
            }
        }

        // Test 3: Check other metric types
        console.log('');
        console.log('🔍 Testing other metric types...');
        
        const otherMetrics = [
            'cpu',
            'memory',
            'disk_read',
            'disk_write',
            'load_1',
            'load_5',
            'load_15'
        ];

        for (const metric of otherMetrics) {
            try {
                const res = await axios.get(
                    `https://api.digitalocean.com/v2/monitoring/metrics/droplet/${metric}?host_id=${droplet.id}&start=${start}&end=${now}`,
                    { headers: { Authorization: `Bearer ${DIGITALOCEAN_TOKEN}` } }
                );
                
                console.log(`✅ ${metric}: Available (${res.data.data?.result?.length || 0} results)`);
            } catch (error) {
                console.log(`❌ ${metric}: ${error.response?.status}`);
            }
        }

        // Test 4: Check account limits and features
        console.log('');
        console.log('🔍 Checking account information...');
        
        try {
            const accountRes = await axios.get('https://api.digitalocean.com/v2/account', {
                headers: { Authorization: `Bearer ${DIGITALOCEAN_TOKEN}` }
            });
            
            console.log(`📊 Account status: ${accountRes.data.account.status}`);
            console.log(`📊 Account email verified: ${accountRes.data.account.email_verified}`);
            
        } catch (error) {
            console.log(`❌ Account info error: ${error.message}`);
        }

        // Test 5: Raw API response inspection
        console.log('');
        console.log('🔍 Raw API response inspection...');
        
        try {
            const rawRes = await axios.get(
                `https://api.digitalocean.com/v2/monitoring/metrics/droplet/bandwidth?host_id=${droplet.id}&interface=public&direction=inbound&start=${start}&end=${now}`,
                { headers: { Authorization: `Bearer ${DIGITALOCEAN_TOKEN}` } }
            );
            
            console.log('📊 Full response structure:');
            console.log(JSON.stringify(rawRes.data, null, 2));
            
        } catch (error) {
            console.log(`❌ Raw response error: ${error.message}`);
        }

    } catch (error) {
        console.error('❌ Diagnosis failed:', error.message);
    }

    console.log('');
    console.log('🎯 POSSIBLE CAUSES:');
    console.log('==================');
    console.log('');
    console.log('1. 📊 Monitoring not enabled on droplet');
    console.log('2. 🕐 Very long metric delays (24-48+ hours)');
    console.log('3. 🔧 Different traffic measurement method');
    console.log('4. 🌐 Downloads not going through monitored interface');
    console.log('5. 📈 Metrics aggregation issues');
    console.log('6. 🔑 API permissions or account limitations');
    console.log('');
    console.log('💡 RECOMMENDATIONS:');
    console.log('===================');
    console.log('');
    console.log('1. Enable monitoring on droplet if not enabled');
    console.log('2. Wait 24-48 hours and check again');
    console.log('3. Contact DigitalOcean support about metrics');
    console.log('4. Consider DigitalOcean may not track download traffic');
}

diagnoseDigitalOceanIssue().catch(console.error);
