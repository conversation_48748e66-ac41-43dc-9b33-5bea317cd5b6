#!/bin/bash

# Setup script for Docker bandwidth monitoring service
set -e

echo "🚀 Setting up Manidae Cloud Bandwidth Monitor for Docker..."

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p gcp-keys

# Copy environment template if .env.docker doesn't exist
if [ ! -f ".env.docker" ]; then
    echo "📋 Creating .env.docker from template..."
    cp .env.docker .env.docker.example
    echo "⚠️  Please edit .env.docker with your actual API credentials and settings"
else
    echo "✅ .env.docker already exists"
fi

# Check if GCP credentials are needed
echo ""
echo "🔧 GCP Setup (optional):"
echo "If you're using Google Cloud Platform:"
echo "1. Place your GCP service account key file in: ./scripts/gcp-keys/"
echo "2. Update GCP_CREDENTIALS_FILE in .env.docker to: /app/keys/your-service-account-key.json"
echo ""

# Display next steps
echo "✅ Setup complete!"
echo ""
echo "📝 Next steps:"
echo "1. Edit ./scripts/.env.docker with your API credentials"
echo "2. If using GCP, place your service account key in ./scripts/gcp-keys/"
echo "3. Run: docker compose up -d bandwidth_monitor"
echo "4. Check logs: docker compose logs -f bandwidth_monitor"
echo ""
echo "🔍 To test the configuration:"
echo "   docker compose run --rm bandwidth_monitor node -e \"console.log('Test successful')\""
echo ""
echo "⚠️  Remember to set ENABLE_INSTANCE_TERMINATION=true in production!"
