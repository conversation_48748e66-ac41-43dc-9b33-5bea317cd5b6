import React, { useState } from 'react';
import Modal from './ui/modal';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';

interface ReferralRequestModalProps {
  open: boolean;
  onClose: () => void;
}

const ReferralRequestModal: React.FC<ReferralRequestModalProps> = ({ open, onClose }) => {
  const [email, setEmail] = useState('');
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');

  const referralAmount = import.meta.env.VITE_REFERRAL_CREDIT_AMOUNT || '5';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim() || !reason.trim()) {
      setSubmitMessage('Please fill in both fields');
      return;
    }

    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      // For now, we'll just simulate a successful submission
      // In a real implementation, you'd send this to your backend
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSubmitMessage('Request submitted successfully! We will review your request and send you a referral code if approved.');
      
      // Clear form after successful submission
      setTimeout(() => {
        setEmail('');
        setReason('');
        setSubmitMessage('');
        onClose();
      }, 3000);
      
    } catch (error: any) {
      setSubmitMessage(error.message || 'Failed to submit request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setEmail('');
    setReason('');
    setSubmitMessage('');
    onClose();
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      title="Request Free Credits"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-4">
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <h3 className="font-semibold text-green-800 mb-2">
              🎁 Get €{referralAmount} Free Credits
            </h3>
            <p className="text-sm text-green-700">
              Tell us why you'd like to try Manidae Cloud and we may send you a referral code for €{referralAmount} in free credits!
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isSubmitting}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Why do you want to try Manidae Cloud?</Label>
            <textarea
              id="reason"
              className="flex min-h-[120px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Tell us about your project, what you want to deploy, or how you plan to use our platform..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              disabled={isSubmitting}
              required
            />
          </div>

          {submitMessage && (
            <div className={`p-3 rounded-md text-sm ${
              submitMessage.includes('success') || submitMessage.includes('submitted')
                ? 'bg-green-50 text-green-700 border border-green-200'
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {submitMessage}
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <p className="text-xs text-blue-700">
              <strong>Note:</strong> Credits expire after 30 days and can be used for any deployment. 
              We review all requests manually and aim to respond within 24 hours.
            </p>
          </div>
        </div>

        <div className="flex justify-end gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || !email.trim() || !reason.trim()}
          >
            {isSubmitting ? 'Submitting...' : 'Request Credits'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default ReferralRequestModal;
